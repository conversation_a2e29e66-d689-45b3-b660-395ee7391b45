[0.011s] Invoking command in '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/kilted:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ROS2_WS/my_calculator/build/my_calculator -- -j12 -l12
[0.039s] -- Found ament_cmake: 2.7.3 (/opt/ros/kilted/share/ament_cmake/cmake)
[0.065s] -- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter 
[0.146s] -- Found rclcpp: 29.5.0 (/opt/ros/kilted/share/rclcpp/cmake)
[0.176s] -- Found rosidl_generator_c: 4.9.4 (/opt/ros/kilted/share/rosidl_generator_c/cmake)
[0.185s] -- Found rosidl_generator_cpp: 4.9.4 (/opt/ros/kilted/share/rosidl_generator_cpp/cmake)
[0.199s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.214s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.283s] -- Found rmw_implementation_cmake: 7.8.2 (/opt/ros/kilted/share/rmw_implementation_cmake/cmake)
[0.285s] -- Found rmw_fastrtps_cpp: 9.3.2 (/opt/ros/kilted/share/rmw_fastrtps_cpp/cmake)
[0.327s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.400s] -- Found rosidl_default_generators: 1.7.1 (/opt/ros/kilted/share/rosidl_default_generators/cmake)
[0.410s] -- Found rosidl_adapter: 4.9.4 (/opt/ros/kilted/share/rosidl_adapter/cmake)
[0.687s] -- Found ament_cmake_ros_core: 0.14.3 (/opt/ros/kilted/share/ament_cmake_ros_core/cmake)
[0.844s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.904s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[1.155s] -- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter Development NumPy Development.Module Development.Embed 
[1.164s] [31mCMake Error at CMakeLists.txt:41 (target_link_libraries):
[1.164s]   The keyword signature for target_link_libraries has already been used with
[1.165s]   the target "arithmetic_server".  All uses of target_link_libraries with a
[1.165s]   target must be either all-keyword or all-plain.
[1.165s] 
[1.165s]   The uses of the keyword signature are here:
[1.165s] 
[1.165s]    * CMakeLists.txt:29 (target_link_libraries)
[1.165s] 
[1.165s] 
[1.165s] [0m
[1.165s] [31mCMake Error at CMakeLists.txt:42 (target_link_libraries):
[1.165s]   The keyword signature for target_link_libraries has already been used with
[1.165s]   the target "arithmetic_client".  All uses of target_link_libraries with a
[1.165s]   target must be either all-keyword or all-plain.
[1.165s] 
[1.165s]   The uses of the keyword signature are here:
[1.166s] 
[1.166s]    * CMakeLists.txt:35 (target_link_libraries)
[1.166s] 
[1.166s] 
[1.166s] [0m
[1.169s] -- Configuring incomplete, errors occurred!
[1.178s] gmake: *** [Makefile:1707: cmake_check_build_system] Error 1
[1.180s] Invoked command in '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator' returned '2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/kilted:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ROS2_WS/my_calculator/build/my_calculator -- -j12 -l12
