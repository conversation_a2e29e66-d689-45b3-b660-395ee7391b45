[0.013s] Invoking command in '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/kilted:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ROS2_WS/my_calculator/build/my_calculator -- -j12 -l12
[0.066s] [  4%] [32mBuilding CXX object CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o[0m
[0.066s] [  4%] [32mBuilding CXX object CMakeFiles/arithmetic_server.dir/src/arithmetic_server.cpp.o[0m
[0.066s] [  4%] Built target my_calculator__rosidl_generator_type_description
[0.077s] [  4%] Built target ament_cmake_python_copy_my_calculator
[0.087s] [  5%] Built target my_calculator__cpp
[0.090s] [ 26%] Built target my_calculator__rosidl_generator_c
[0.126s] [ 35%] Built target my_calculator__rosidl_typesupport_introspection_c
[0.127s] [ 44%] Built target my_calculator__rosidl_typesupport_introspection_cpp
[0.127s] [ 52%] Built target my_calculator__rosidl_typesupport_c
[0.127s] [ 64%] Built target my_calculator__rosidl_typesupport_cpp
[0.127s] [ 70%] Built target my_calculator__rosidl_typesupport_fastrtps_c
[0.130s] [ 79%] Built target my_calculator__rosidl_typesupport_fastrtps_cpp
[0.152s] [ 79%] Built target my_calculator
[0.176s] [ 80%] Built target my_calculator__py
[0.202s] [ 88%] Built target my_calculator__rosidl_generator_py
[0.228s] [ 94%] Built target my_calculator_s__rosidl_typesupport_fastrtps_c
[0.228s] [ 94%] Built target my_calculator_s__rosidl_typesupport_introspection_c
[0.229s] [ 97%] Built target my_calculator_s__rosidl_typesupport_c
[0.348s] running egg_info
[0.375s] In file included from [01m[K/home/<USER>/ROS2_WS/my_calculator/src/../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/multiply.hpp:8[m[K,
[0.375s]                  from [01m[K/home/<USER>/ROS2_WS/my_calculator/src/arithmetic_client.cpp:4[m[K:
[0.375s] [01m[K/home/<USER>/ROS2_WS/my_calculator/src/../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/./detail/multiply__builder.hpp:14:10:[m[K [01;31m[Kfatal error: [m[Kmy_calculator/srv/detail/multiply__struct.hpp: No such file or directory
[0.375s]    14 | #include [01;31m[K"my_calculator/srv/detail/multiply__struct.hpp"[m[K
[0.376s]       |          [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[0.376s] compilation terminated.
[0.378s] writing my_calculator.egg-info/PKG-INFO
[0.378s] writing dependency_links to my_calculator.egg-info/dependency_links.txt
[0.379s] writing top-level names to my_calculator.egg-info/top_level.txt
[0.379s] gmake[2]: *** [CMakeFiles/arithmetic_client.dir/build.make:76: CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o] Error 1
[0.379s] gmake[1]: *** [CMakeFiles/Makefile2:651: CMakeFiles/arithmetic_client.dir/all] Error 2
[0.379s] gmake[1]: *** Waiting for unfinished jobs....
[0.382s] In file included from [01m[K/home/<USER>/ROS2_WS/my_calculator/src/../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/multiply.hpp:8[m[K,
[0.383s]                  from [01m[K/home/<USER>/ROS2_WS/my_calculator/src/arithmetic_server.cpp:4[m[K:
[0.383s] [01m[K/home/<USER>/ROS2_WS/my_calculator/src/../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/./detail/multiply__builder.hpp:14:10:[m[K [01;31m[Kfatal error: [m[Kmy_calculator/srv/detail/multiply__struct.hpp: No such file or directory
[0.383s]    14 | #include [01;31m[K"my_calculator/srv/detail/multiply__struct.hpp"[m[K
[0.383s]       |          [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[0.383s] compilation terminated.
[0.385s] gmake[2]: *** [CMakeFiles/arithmetic_server.dir/build.make:76: CMakeFiles/arithmetic_server.dir/src/arithmetic_server.cpp.o] Error 1
[0.386s] gmake[1]: *** [CMakeFiles/Makefile2:625: CMakeFiles/arithmetic_server.dir/all] Error 2
[0.463s] reading manifest file 'my_calculator.egg-info/SOURCES.txt'
[0.464s] writing manifest file 'my_calculator.egg-info/SOURCES.txt'
[0.503s] [ 97%] Built target ament_cmake_python_build_my_calculator_egg
[0.505s] gmake: *** [Makefile:146: all] Error 2
[0.507s] Invoked command in '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator' returned '2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/kilted:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ROS2_WS/my_calculator/build/my_calculator -- -j12 -l12
