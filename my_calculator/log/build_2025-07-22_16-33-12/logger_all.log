[0.135s] DEBUG:colcon:Command line arguments: ['/home/<USER>/.local/bin/colcon', 'build', '--packages-select', 'my_calculator']
[0.135s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=12, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['my_calculator'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x74b8149719a0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x74b814ba36b0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x74b814ba36b0>>, mixin_verb=('build',))
[0.169s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.170s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.170s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.170s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.170s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.170s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.170s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/ROS2_WS/my_calculator'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.196s] DEBUG:colcon.colcon_core.package_identification:Package '.' with type 'ros.ament_cmake' and name 'my_calculator'
[0.196s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.196s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.196s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.196s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.196s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.211s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.211s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.212s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/opt/ros/jazzy' in the environment variable AMENT_PREFIX_PATH doesn't exist
[0.212s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/opt/ros/jazzy/opt/gz_math_vendor' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[0.212s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/opt/ros/jazzy/opt/gz_utils_vendor' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[0.213s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/opt/ros/jazzy/opt/gz_cmake_vendor' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[0.214s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 3 installed packages in /home/<USER>/Fast-DDS/install
[0.215s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 289 installed packages in /opt/ros/kilted
[0.216s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.261s] Level 5:colcon.colcon_core.verb:set package 'my_calculator' build argument 'cmake_args' from command line to 'None'
[0.261s] Level 5:colcon.colcon_core.verb:set package 'my_calculator' build argument 'cmake_target' from command line to 'None'
[0.261s] Level 5:colcon.colcon_core.verb:set package 'my_calculator' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.261s] Level 5:colcon.colcon_core.verb:set package 'my_calculator' build argument 'cmake_clean_cache' from command line to 'False'
[0.261s] Level 5:colcon.colcon_core.verb:set package 'my_calculator' build argument 'cmake_clean_first' from command line to 'False'
[0.261s] Level 5:colcon.colcon_core.verb:set package 'my_calculator' build argument 'cmake_force_configure' from command line to 'False'
[0.261s] Level 5:colcon.colcon_core.verb:set package 'my_calculator' build argument 'ament_cmake_args' from command line to 'None'
[0.261s] Level 5:colcon.colcon_core.verb:set package 'my_calculator' build argument 'catkin_cmake_args' from command line to 'None'
[0.261s] Level 5:colcon.colcon_core.verb:set package 'my_calculator' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.261s] DEBUG:colcon.colcon_core.verb:Building package 'my_calculator' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ROS2_WS/my_calculator/install/my_calculator', 'merge_install': False, 'path': '/home/<USER>/ROS2_WS/my_calculator', 'symlink_install': False, 'test_result_base': None}
[0.262s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.262s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.262s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ROS2_WS/my_calculator' with build type 'ament_cmake'
[0.262s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ROS2_WS/my_calculator'
[0.264s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.264s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.264s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.272s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/kilted:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ROS2_WS/my_calculator/build/my_calculator -- -j12 -l12
[0.783s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator' returned '2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/kilted:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ROS2_WS/my_calculator/build/my_calculator -- -j12 -l12
[0.922s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(my_calculator)
[0.924s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ROS2_WS/my_calculator/install/my_calculator' for CMake module files
[0.925s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ROS2_WS/my_calculator/install/my_calculator' for CMake config files
[0.925s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ROS2_WS/my_calculator/install/my_calculator/bin'
[0.925s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ROS2_WS/my_calculator/install/my_calculator/lib/pkgconfig/my_calculator.pc'
[0.925s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ROS2_WS/my_calculator/install/my_calculator/lib/python3.12/site-packages'
[0.926s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ROS2_WS/my_calculator/install/my_calculator/bin'
[0.926s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ROS2_WS/my_calculator/install/my_calculator/share/my_calculator/package.ps1'
[0.926s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ROS2_WS/my_calculator/install/my_calculator/share/my_calculator/package.dsv'
[0.927s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ROS2_WS/my_calculator/install/my_calculator/share/my_calculator/package.sh'
[0.927s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ROS2_WS/my_calculator/install/my_calculator/share/my_calculator/package.bash'
[0.928s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ROS2_WS/my_calculator/install/my_calculator/share/my_calculator/package.zsh'
[0.929s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ROS2_WS/my_calculator/install/my_calculator/share/colcon-core/packages/my_calculator)
[0.939s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.939s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.939s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '2'
[0.939s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.944s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.944s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.944s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[0.958s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[0.958s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ROS2_WS/my_calculator/install/local_setup.ps1'
[0.959s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ROS2_WS/my_calculator/install/_local_setup_util_ps1.py'
[0.960s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ROS2_WS/my_calculator/install/setup.ps1'
[0.962s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ROS2_WS/my_calculator/install/local_setup.sh'
[0.962s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ROS2_WS/my_calculator/install/_local_setup_util_sh.py'
[0.963s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ROS2_WS/my_calculator/install/setup.sh'
[0.964s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ROS2_WS/my_calculator/install/local_setup.bash'
[0.964s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ROS2_WS/my_calculator/install/setup.bash'
[0.965s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ROS2_WS/my_calculator/install/local_setup.zsh'
[0.965s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ROS2_WS/my_calculator/install/setup.zsh'
