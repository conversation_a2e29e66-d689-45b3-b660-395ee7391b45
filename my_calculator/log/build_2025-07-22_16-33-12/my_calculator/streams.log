[0.009s] Invoking command in '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/kilted:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ROS2_WS/my_calculator/build/my_calculator -- -j12 -l12
[0.062s] [  2%] Built target my_calculator__rosidl_generator_type_description
[0.062s] [  2%] [32mBuilding CXX object CMakeFiles/arithmetic_server.dir/src/arithmetic_server.cpp.o[0m
[0.062s] [  4%] [32mBuilding CXX object CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o[0m
[0.071s] [  4%] Built target ament_cmake_python_copy_my_calculator
[0.081s] [  5%] Built target my_calculator__cpp
[0.084s] [ 26%] Built target my_calculator__rosidl_generator_c
[0.118s] [ 36%] Built target my_calculator__rosidl_typesupport_cpp
[0.118s] [ 44%] Built target my_calculator__rosidl_typesupport_fastrtps_c
[0.119s] [ 61%] Built target my_calculator__rosidl_typesupport_introspection_c
[0.119s] [ 61%] Built target my_calculator__rosidl_typesupport_c
[0.120s] [ 73%] Built target my_calculator__rosidl_typesupport_fastrtps_cpp
[0.120s] [ 79%] Built target my_calculator__rosidl_typesupport_introspection_cpp
[0.144s] [ 79%] Built target my_calculator
[0.165s] [ 80%] Built target my_calculator__py
[0.195s] [ 82%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_py.dir/rosidl_generator_py/my_calculator/srv/_add_s.c.o[0m
[0.196s] [ 83%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_py.dir/rosidl_generator_py/my_calculator/srv/_subtract_s.c.o[0m
[0.199s] [ 85%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_py.dir/rosidl_generator_py/my_calculator/srv/_multiply_s.c.o[0m
[0.199s] [ 86%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_py.dir/rosidl_generator_py/my_calculator/srv/_divide_s.c.o[0m
[0.378s] running egg_info
[0.399s] In file included from [01m[K/home/<USER>/ROS2_WS/my_calculator/src/../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/subtract.hpp:8[m[K,
[0.399s]                  from [01m[K/home/<USER>/ROS2_WS/my_calculator/src/arithmetic_server.cpp:3[m[K:
[0.400s] [01m[K/home/<USER>/ROS2_WS/my_calculator/src/../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/./detail/subtract__builder.hpp:14:10:[m[K [01;31m[Kfatal error: [m[Kmy_calculator/srv/detail/subtract__struct.hpp: No such file or directory
[0.400s]    14 | #include [01;31m[K"my_calculator/srv/detail/subtract__struct.hpp"[m[K
[0.400s]       |          [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[0.400s] compilation terminated.
[0.402s] gmake[2]: *** [CMakeFiles/arithmetic_server.dir/build.make:76: CMakeFiles/arithmetic_server.dir/src/arithmetic_server.cpp.o] Error 1
[0.403s] gmake[1]: *** [CMakeFiles/Makefile2:625: CMakeFiles/arithmetic_server.dir/all] Error 2
[0.403s] gmake[1]: *** Waiting for unfinished jobs....
[0.405s] In file included from [01m[K/home/<USER>/ROS2_WS/my_calculator/src/../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/subtract.hpp:8[m[K,
[0.406s]                  from [01m[K/home/<USER>/ROS2_WS/my_calculator/src/arithmetic_client.cpp:3[m[K:
[0.406s] [01m[K/home/<USER>/ROS2_WS/my_calculator/src/../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/./detail/subtract__builder.hpp:14:10:[m[K [01;31m[Kfatal error: [m[Kmy_calculator/srv/detail/subtract__struct.hpp: No such file or directory
[0.406s]    14 | #include [01;31m[K"my_calculator/srv/detail/subtract__struct.hpp"[m[K
[0.406s]       |          [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[0.406s] compilation terminated.
[0.412s] writing my_calculator.egg-info/PKG-INFO
[0.412s] writing dependency_links to my_calculator.egg-info/dependency_links.txt
[0.412s] writing top-level names to my_calculator.egg-info/top_level.txt
[0.426s] gmake[2]: *** [CMakeFiles/arithmetic_client.dir/build.make:76: CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o] Error 1
[0.426s] gmake[1]: *** [CMakeFiles/Makefile2:651: CMakeFiles/arithmetic_client.dir/all] Error 2
[0.471s] [ 88%] [32m[1mLinking C shared library libmy_calculator__rosidl_generator_py.so[0m
[0.485s] reading manifest file 'my_calculator.egg-info/SOURCES.txt'
[0.485s] writing manifest file 'my_calculator.egg-info/SOURCES.txt'
[0.517s] [ 88%] Built target my_calculator__rosidl_generator_py
[0.517s] [ 88%] Built target ament_cmake_python_build_my_calculator_egg
[0.518s] gmake: *** [Makefile:146: all] Error 2
[0.520s] Invoked command in '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator' returned '2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/kilted:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ROS2_WS/my_calculator/build/my_calculator -- -j12 -l12
