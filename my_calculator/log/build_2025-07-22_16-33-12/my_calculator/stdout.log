[  2%] Built target my_calculator__rosidl_generator_type_description
[  2%] [32mBuilding CXX object CMakeFiles/arithmetic_server.dir/src/arithmetic_server.cpp.o[0m
[  4%] [32mBuilding CXX object CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o[0m
[  4%] Built target ament_cmake_python_copy_my_calculator
[  5%] Built target my_calculator__cpp
[ 26%] Built target my_calculator__rosidl_generator_c
[ 36%] Built target my_calculator__rosidl_typesupport_cpp
[ 44%] Built target my_calculator__rosidl_typesupport_fastrtps_c
[ 61%] Built target my_calculator__rosidl_typesupport_introspection_c
[ 61%] Built target my_calculator__rosidl_typesupport_c
[ 73%] Built target my_calculator__rosidl_typesupport_fastrtps_cpp
[ 79%] Built target my_calculator__rosidl_typesupport_introspection_cpp
[ 79%] Built target my_calculator
[ 80%] Built target my_calculator__py
[ 82%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_py.dir/rosidl_generator_py/my_calculator/srv/_add_s.c.o[0m
[ 83%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_py.dir/rosidl_generator_py/my_calculator/srv/_subtract_s.c.o[0m
[ 85%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_py.dir/rosidl_generator_py/my_calculator/srv/_multiply_s.c.o[0m
[ 86%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_py.dir/rosidl_generator_py/my_calculator/srv/_divide_s.c.o[0m
running egg_info
writing my_calculator.egg-info/PKG-INFO
writing dependency_links to my_calculator.egg-info/dependency_links.txt
writing top-level names to my_calculator.egg-info/top_level.txt
[ 88%] [32m[1mLinking C shared library libmy_calculator__rosidl_generator_py.so[0m
reading manifest file 'my_calculator.egg-info/SOURCES.txt'
writing manifest file 'my_calculator.egg-info/SOURCES.txt'
[ 88%] Built target my_calculator__rosidl_generator_py
[ 88%] Built target ament_cmake_python_build_my_calculator_egg
