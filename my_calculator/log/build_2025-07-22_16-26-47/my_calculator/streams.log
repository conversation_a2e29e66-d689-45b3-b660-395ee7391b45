[0.013s] Invoking command in '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/kilted:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ROS2_WS/my_calculator/build/my_calculator -- -j12 -l12
[0.069s] [  2%] [32mBuilding CXX object CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o[0m
[0.069s] [  4%] [32mBuilding CXX object CMakeFiles/arithmetic_server.dir/src/arithmetic_server.cpp.o[0m
[0.070s] [  4%] Built target my_calculator__rosidl_generator_type_description
[0.079s] [  4%] Built target ament_cmake_python_copy_my_calculator
[0.094s] [  5%] Built target my_calculator__cpp
[0.097s] [ 26%] Built target my_calculator__rosidl_generator_c
[0.135s] [ 35%] Built target my_calculator__rosidl_typesupport_cpp
[0.135s] [ 44%] Built target my_calculator__rosidl_typesupport_fastrtps_cpp
[0.135s] [ 52%] Built target my_calculator__rosidl_typesupport_introspection_c
[0.135s] [ 61%] Built target my_calculator__rosidl_typesupport_c
[0.137s] [ 70%] Built target my_calculator__rosidl_typesupport_fastrtps_c
[0.137s] [ 79%] Built target my_calculator__rosidl_typesupport_introspection_cpp
[0.159s] [ 79%] Built target my_calculator
[0.180s] [ 80%] Built target my_calculator__py
[0.205s] [ 88%] Built target my_calculator__rosidl_generator_py
[0.232s] [ 91%] Built target my_calculator_s__rosidl_typesupport_fastrtps_c
[0.236s] [ 94%] Built target my_calculator_s__rosidl_typesupport_c
[0.238s] [ 97%] Built target my_calculator_s__rosidl_typesupport_introspection_c
[0.369s] running egg_info
[0.381s] In file included from [01m[K/home/<USER>/ROS2_WS/my_calculator/src/arithmetic_server.cpp:2[m[K:
[0.381s] [01m[K/home/<USER>/ROS2_WS/my_calculator/src/../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/add.hpp:7:10:[m[K [01;31m[Kfatal error: [m[Kmy_calculator/srv/detail/add__struct.hpp: No such file or directory
[0.381s]     7 | #include [01;31m[K"my_calculator/srv/detail/add__struct.hpp"[m[K
[0.381s]       |          [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[0.381s] compilation terminated.
[0.384s] gmake[2]: *** [CMakeFiles/arithmetic_server.dir/build.make:76: CMakeFiles/arithmetic_server.dir/src/arithmetic_server.cpp.o] Error 1
[0.385s] gmake[1]: *** [CMakeFiles/Makefile2:625: CMakeFiles/arithmetic_server.dir/all] Error 2
[0.385s] gmake[1]: *** Waiting for unfinished jobs....
[0.392s] In file included from [01m[K/home/<USER>/ROS2_WS/my_calculator/src/arithmetic_client.cpp:2[m[K:
[0.392s] [01m[K/home/<USER>/ROS2_WS/my_calculator/src/../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/add.hpp:7:10:[m[K [01;31m[Kfatal error: [m[Kmy_calculator/srv/detail/add__struct.hpp: No such file or directory
[0.392s]     7 | #include [01;31m[K"my_calculator/srv/detail/add__struct.hpp"[m[K
[0.392s]       |          [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[0.392s] compilation terminated.
[0.395s] gmake[2]: *** [CMakeFiles/arithmetic_client.dir/build.make:76: CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o] Error 1
[0.395s] gmake[1]: *** [CMakeFiles/Makefile2:651: CMakeFiles/arithmetic_client.dir/all] Error 2
[0.396s] writing my_calculator.egg-info/PKG-INFO
[0.397s] writing dependency_links to my_calculator.egg-info/dependency_links.txt
[0.397s] writing top-level names to my_calculator.egg-info/top_level.txt
[0.478s] reading manifest file 'my_calculator.egg-info/SOURCES.txt'
[0.479s] writing manifest file 'my_calculator.egg-info/SOURCES.txt'
[0.510s] [ 97%] Built target ament_cmake_python_build_my_calculator_egg
[0.511s] gmake: *** [Makefile:146: all] Error 2
[0.513s] Invoked command in '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator' returned '2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/kilted:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ROS2_WS/my_calculator/build/my_calculator -- -j12 -l12
