[  2%] [32mBuilding CXX object CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o[0m
[  4%] [32mBuilding CXX object CMakeFiles/arithmetic_server.dir/src/arithmetic_server.cpp.o[0m
[  4%] Built target my_calculator__rosidl_generator_type_description
[  4%] Built target ament_cmake_python_copy_my_calculator
[  5%] Built target my_calculator__cpp
[ 26%] Built target my_calculator__rosidl_generator_c
[ 35%] Built target my_calculator__rosidl_typesupport_cpp
[ 44%] Built target my_calculator__rosidl_typesupport_fastrtps_cpp
[ 52%] Built target my_calculator__rosidl_typesupport_introspection_c
[ 61%] Built target my_calculator__rosidl_typesupport_c
[ 70%] Built target my_calculator__rosidl_typesupport_fastrtps_c
[ 79%] Built target my_calculator__rosidl_typesupport_introspection_cpp
[ 79%] Built target my_calculator
[ 80%] Built target my_calculator__py
[ 88%] Built target my_calculator__rosidl_generator_py
[ 91%] Built target my_calculator_s__rosidl_typesupport_fastrtps_c
[ 94%] Built target my_calculator_s__rosidl_typesupport_c
[ 97%] Built target my_calculator_s__rosidl_typesupport_introspection_c
running egg_info
In file included from [01m[K/home/<USER>/ROS2_WS/my_calculator/src/arithmetic_server.cpp:2[m[K:
[01m[K/home/<USER>/ROS2_WS/my_calculator/src/../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/add.hpp:7:10:[m[K [01;31m[Kfatal error: [m[Kmy_calculator/srv/detail/add__struct.hpp: No such file or directory
    7 | #include [01;31m[K"my_calculator/srv/detail/add__struct.hpp"[m[K
      |          [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
compilation terminated.
gmake[2]: *** [CMakeFiles/arithmetic_server.dir/build.make:76: CMakeFiles/arithmetic_server.dir/src/arithmetic_server.cpp.o] Error 1
gmake[1]: *** [CMakeFiles/Makefile2:625: CMakeFiles/arithmetic_server.dir/all] Error 2
gmake[1]: *** Waiting for unfinished jobs....
In file included from [01m[K/home/<USER>/ROS2_WS/my_calculator/src/arithmetic_client.cpp:2[m[K:
[01m[K/home/<USER>/ROS2_WS/my_calculator/src/../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/add.hpp:7:10:[m[K [01;31m[Kfatal error: [m[Kmy_calculator/srv/detail/add__struct.hpp: No such file or directory
    7 | #include [01;31m[K"my_calculator/srv/detail/add__struct.hpp"[m[K
      |          [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
compilation terminated.
gmake[2]: *** [CMakeFiles/arithmetic_client.dir/build.make:76: CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o] Error 1
gmake[1]: *** [CMakeFiles/Makefile2:651: CMakeFiles/arithmetic_client.dir/all] Error 2
writing my_calculator.egg-info/PKG-INFO
writing dependency_links to my_calculator.egg-info/dependency_links.txt
writing top-level names to my_calculator.egg-info/top_level.txt
reading manifest file 'my_calculator.egg-info/SOURCES.txt'
writing manifest file 'my_calculator.egg-info/SOURCES.txt'
[ 97%] Built target ament_cmake_python_build_my_calculator_egg
gmake: *** [Makefile:146: all] Error 2
