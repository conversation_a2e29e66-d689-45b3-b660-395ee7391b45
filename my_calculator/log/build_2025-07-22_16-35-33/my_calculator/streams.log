[0.011s] Invoking command in '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/kilted:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ROS2_WS/my_calculator/build/my_calculator -- -j12 -l12
[0.066s] [  1%] Built target my_calculator__rosidl_generator_type_description
[0.077s] [  4%] [32mBuilding CXX object CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o[0m
[0.077s] [  4%] [32mBuilding CXX object CMakeFiles/arithmetic_server.dir/src/arithmetic_server.cpp.o[0m
[0.077s] [  4%] Built target ament_cmake_python_copy_my_calculator
[0.088s] [  5%] Built target my_calculator__cpp
[0.092s] [ 26%] Built target my_calculator__rosidl_generator_c
[0.132s] [ 35%] Built target my_calculator__rosidl_typesupport_introspection_c
[0.135s] [ 48%] Built target my_calculator__rosidl_typesupport_cpp
[0.135s] [ 66%] Built target my_calculator__rosidl_typesupport_fastrtps_cpp
[0.135s] [ 75%] Built target my_calculator__rosidl_typesupport_introspection_cpp
[0.141s] [ 76%] Built target my_calculator__rosidl_typesupport_c
[0.141s] [ 79%] Built target my_calculator__rosidl_typesupport_fastrtps_c
[0.180s] [ 79%] Built target my_calculator
[0.216s] [ 80%] Built target my_calculator__py
[0.273s] [ 88%] Built target my_calculator__rosidl_generator_py
[0.315s] [ 91%] Built target my_calculator_s__rosidl_typesupport_fastrtps_c
[0.322s] [ 94%] Built target my_calculator_s__rosidl_typesupport_introspection_c
[0.326s] [ 97%] Built target my_calculator_s__rosidl_typesupport_c
[0.459s] running egg_info
[0.490s] writing my_calculator.egg-info/PKG-INFO
[0.490s] In file included from [01m[K/home/<USER>/ROS2_WS/my_calculator/src/../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/divide.hpp:10[m[K,
[0.490s]                  from [01m[K/home/<USER>/ROS2_WS/my_calculator/src/arithmetic_server.cpp:5[m[K:
[0.491s] [01m[K/home/<USER>/ROS2_WS/my_calculator/src/../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/./detail/divide__type_support.hpp:10:10:[m[K [01;31m[Kfatal error: [m[Kmy_calculator/build/my_calculator/rosidl_generator_cpp/my_calculator/msg/rosidl_generator_cpp__visibility_control.hpp: No such file or directory
[0.491s]    10 | #include [01;31m[K"my_calculator/build/my_calculator/rosidl_generator_cpp/my_calculator/msg/rosidl_generator_cpp__visibility_control.hpp"[m[K
[0.491s]       |          [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[0.491s] compilation terminated.
[0.491s] writing dependency_links to my_calculator.egg-info/dependency_links.txt
[0.491s] writing top-level names to my_calculator.egg-info/top_level.txt
[0.494s] gmake[2]: *** [CMakeFiles/arithmetic_server.dir/build.make:76: CMakeFiles/arithmetic_server.dir/src/arithmetic_server.cpp.o] Error 1
[0.494s] gmake[1]: *** [CMakeFiles/Makefile2:625: CMakeFiles/arithmetic_server.dir/all] Error 2
[0.494s] gmake[1]: *** Waiting for unfinished jobs....
[0.507s] In file included from [01m[K/home/<USER>/ROS2_WS/my_calculator/src/../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/divide.hpp:10[m[K,
[0.507s]                  from [01m[K/home/<USER>/ROS2_WS/my_calculator/src/arithmetic_client.cpp:5[m[K:
[0.508s] [01m[K/home/<USER>/ROS2_WS/my_calculator/src/../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/./detail/divide__type_support.hpp:10:10:[m[K [01;31m[Kfatal error: [m[Kmy_calculator/build/my_calculator/rosidl_generator_cpp/my_calculator/msg/rosidl_generator_cpp__visibility_control.hpp: No such file or directory
[0.508s]    10 | #include [01;31m[K"my_calculator/build/my_calculator/rosidl_generator_cpp/my_calculator/msg/rosidl_generator_cpp__visibility_control.hpp"[m[K
[0.508s]       |          [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[0.508s] compilation terminated.
[0.510s] gmake[2]: *** [CMakeFiles/arithmetic_client.dir/build.make:76: CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o] Error 1
[0.511s] gmake[1]: *** [CMakeFiles/Makefile2:651: CMakeFiles/arithmetic_client.dir/all] Error 2
[0.567s] reading manifest file 'my_calculator.egg-info/SOURCES.txt'
[0.567s] writing manifest file 'my_calculator.egg-info/SOURCES.txt'
[0.596s] [ 97%] Built target ament_cmake_python_build_my_calculator_egg
[0.597s] gmake: *** [Makefile:146: all] Error 2
[0.599s] Invoked command in '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator' returned '2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/kilted:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ROS2_WS/my_calculator/build/my_calculator -- -j12 -l12
