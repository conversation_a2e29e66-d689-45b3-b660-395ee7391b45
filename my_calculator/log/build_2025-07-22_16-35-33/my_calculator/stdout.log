[  1%] Built target my_calculator__rosidl_generator_type_description
[  4%] [32mBuilding CXX object CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o[0m
[  4%] [32mBuilding CXX object CMakeFiles/arithmetic_server.dir/src/arithmetic_server.cpp.o[0m
[  4%] Built target ament_cmake_python_copy_my_calculator
[  5%] Built target my_calculator__cpp
[ 26%] Built target my_calculator__rosidl_generator_c
[ 35%] Built target my_calculator__rosidl_typesupport_introspection_c
[ 48%] Built target my_calculator__rosidl_typesupport_cpp
[ 66%] Built target my_calculator__rosidl_typesupport_fastrtps_cpp
[ 75%] Built target my_calculator__rosidl_typesupport_introspection_cpp
[ 76%] Built target my_calculator__rosidl_typesupport_c
[ 79%] Built target my_calculator__rosidl_typesupport_fastrtps_c
[ 79%] Built target my_calculator
[ 80%] Built target my_calculator__py
[ 88%] Built target my_calculator__rosidl_generator_py
[ 91%] Built target my_calculator_s__rosidl_typesupport_fastrtps_c
[ 94%] Built target my_calculator_s__rosidl_typesupport_introspection_c
[ 97%] Built target my_calculator_s__rosidl_typesupport_c
running egg_info
writing my_calculator.egg-info/PKG-INFO
writing dependency_links to my_calculator.egg-info/dependency_links.txt
writing top-level names to my_calculator.egg-info/top_level.txt
reading manifest file 'my_calculator.egg-info/SOURCES.txt'
writing manifest file 'my_calculator.egg-info/SOURCES.txt'
[ 97%] Built target ament_cmake_python_build_my_calculator_egg
