[  1%] Built target my_calculator__rosidl_generator_type_description
[  4%] [32mBuilding CXX object CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o[0m
[  4%] [32mBuilding CXX object CMakeFiles/arithmetic_server.dir/src/arithmetic_server.cpp.o[0m
[  4%] Built target ament_cmake_python_copy_my_calculator
[  5%] Built target my_calculator__cpp
[ 26%] Built target my_calculator__rosidl_generator_c
[ 35%] Built target my_calculator__rosidl_typesupport_introspection_c
[ 48%] Built target my_calculator__rosidl_typesupport_cpp
[ 66%] Built target my_calculator__rosidl_typesupport_fastrtps_cpp
[ 75%] Built target my_calculator__rosidl_typesupport_introspection_cpp
[ 76%] Built target my_calculator__rosidl_typesupport_c
[ 79%] Built target my_calculator__rosidl_typesupport_fastrtps_c
[ 79%] Built target my_calculator
[ 80%] Built target my_calculator__py
[ 88%] Built target my_calculator__rosidl_generator_py
[ 91%] Built target my_calculator_s__rosidl_typesupport_fastrtps_c
[ 94%] Built target my_calculator_s__rosidl_typesupport_introspection_c
[ 97%] Built target my_calculator_s__rosidl_typesupport_c
running egg_info
writing my_calculator.egg-info/PKG-INFO
In file included from [01m[K/home/<USER>/ROS2_WS/my_calculator/src/../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/divide.hpp:10[m[K,
                 from [01m[K/home/<USER>/ROS2_WS/my_calculator/src/arithmetic_server.cpp:5[m[K:
[01m[K/home/<USER>/ROS2_WS/my_calculator/src/../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/./detail/divide__type_support.hpp:10:10:[m[K [01;31m[Kfatal error: [m[Kmy_calculator/build/my_calculator/rosidl_generator_cpp/my_calculator/msg/rosidl_generator_cpp__visibility_control.hpp: No such file or directory
   10 | #include [01;31m[K"my_calculator/build/my_calculator/rosidl_generator_cpp/my_calculator/msg/rosidl_generator_cpp__visibility_control.hpp"[m[K
      |          [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
compilation terminated.
writing dependency_links to my_calculator.egg-info/dependency_links.txt
writing top-level names to my_calculator.egg-info/top_level.txt
gmake[2]: *** [CMakeFiles/arithmetic_server.dir/build.make:76: CMakeFiles/arithmetic_server.dir/src/arithmetic_server.cpp.o] Error 1
gmake[1]: *** [CMakeFiles/Makefile2:625: CMakeFiles/arithmetic_server.dir/all] Error 2
gmake[1]: *** Waiting for unfinished jobs....
In file included from [01m[K/home/<USER>/ROS2_WS/my_calculator/src/../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/divide.hpp:10[m[K,
                 from [01m[K/home/<USER>/ROS2_WS/my_calculator/src/arithmetic_client.cpp:5[m[K:
[01m[K/home/<USER>/ROS2_WS/my_calculator/src/../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/./detail/divide__type_support.hpp:10:10:[m[K [01;31m[Kfatal error: [m[Kmy_calculator/build/my_calculator/rosidl_generator_cpp/my_calculator/msg/rosidl_generator_cpp__visibility_control.hpp: No such file or directory
   10 | #include [01;31m[K"my_calculator/build/my_calculator/rosidl_generator_cpp/my_calculator/msg/rosidl_generator_cpp__visibility_control.hpp"[m[K
      |          [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
compilation terminated.
gmake[2]: *** [CMakeFiles/arithmetic_client.dir/build.make:76: CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o] Error 1
gmake[1]: *** [CMakeFiles/Makefile2:651: CMakeFiles/arithmetic_client.dir/all] Error 2
reading manifest file 'my_calculator.egg-info/SOURCES.txt'
writing manifest file 'my_calculator.egg-info/SOURCES.txt'
[ 97%] Built target ament_cmake_python_build_my_calculator_egg
gmake: *** [Makefile:146: all] Error 2
