[0.000000] (-) TimerEvent: {}
[0.000745] (my_calculator) JobQueued: {'identifier': 'my_calculator', 'dependencies': OrderedDict()}
[0.000782] (my_calculator) JobStarted: {'identifier': 'my_calculator'}
[0.009531] (my_calculator) JobProgress: {'identifier': 'my_calculator', 'progress': 'cmake'}
[0.010726] (my_calculator) JobProgress: {'identifier': 'my_calculator', 'progress': 'build'}
[0.010759] (my_calculator) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator', '--', '-j12', '-l12'], 'cwd': '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_AU:en', 'SYSROOT_64': '/home/<USER>/ti_sdk/linux-devkit/sysroots/aarch64-oe-linux', 'USER': 'duythien', 'LC_TIME': 'vi_VN.UTF-8', 'CC_64': '/home/<USER>/ti_sdk/linux-devkit/sysroots/x86_64-arago-linux/usr/bin/aarch64-oe-linux/aarch64-oe-linux-gcc --sysroot=/home/<USER>/ti_sdk/linux-devkit/sysroots/aarch64-oe-linux', 'XDG_SESSION_TYPE': 'x11', 'GIT_ASKPASS': '/usr/share/code/resources/app/extensions/git/dist/askpass.sh', 'ROS_APT_SOURCE_VERSION': '1.1.0', 'SHLVL': '2', 'LD_LIBRARY_PATH': '/opt/ros/kilted/opt/zenoh_cpp_vendor/lib:/opt/ros/kilted/opt/gz_math_vendor/lib:/opt/ros/kilted/opt/gz_utils_vendor/lib:/opt/ros/kilted/opt/rviz_ogre_vendor/lib:/opt/ros/kilted/lib/x86_64-linux-gnu:/opt/ros/kilted/opt/gz_cmake_vendor/lib:/opt/ros/kilted/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib:/home/<USER>/Fast-DDS/install/fastdds/lib:/home/<USER>/Fast-DDS/install/fastcdr/lib', 'HOME': '/home/<USER>', 'CHROME_DESKTOP': 'code.desktop', 'OLDPWD': '/home/<USER>/ROS2_WS', 'TERM_PROGRAM_VERSION': '1.95.1', 'DESKTOP_SESSION': 'ubuntu', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'VSCODE_GIT_ASKPASS_MAIN': '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js', 'LC_MONETARY': 'vi_VN.UTF-8', 'VSCODE_GIT_ASKPASS_NODE': '/usr/share/code/code', 'MANAGERPID': '2429', 'DBUS_STARTER_BUS_TYPE': 'session', 'SYSTEMD_EXEC_PID': '2858', 'GSM_SKIP_SSH_AGENT_WORKAROUND': 'true', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus,guid=13c8833733f832d27a7c73e9687f00e8', 'COLORTERM': 'truecolor', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'COLCON_PREFIX_PATH': '/home/<USER>/Fast-DDS/install', 'ROS_DISTRO': 'kilted', 'LOGNAME': 'duythien', 'CROSS_COMPILE_32': '/home/<USER>/ti_sdk/k3r5-devkit/sysroots/x86_64-arago-linux/usr/bin/arm-oe-eabi/arm-oe-eabi-', 'JOURNAL_STREAM': '9:16178', '_': '/home/<USER>/.local/bin/colcon', 'ROS_VERSION': '2', 'PKG_CONFIG_PATH': '/home/<USER>/Fast-DDS/install/foonathan_memory_vendor/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/Fast-DDS/install/foonathan_memory_vendor/lib/pkgconfig', 'MEMORY_PRESSURE_WATCH': '/sys/fs/cgroup/user.slice/user-1000.slice/<EMAIL>/app.slice/app-gnome\\x2dsession\\x2dmanager.slice/<EMAIL>/memory.pressure', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'duythien', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'WINDOWPATH': '2', 'PATH': '/opt/ros/kilted/bin:/opt/ros/jazzy/bin:/home/<USER>/Fast-DDS/install/fastdds/bin:/home/<USER>/Fast-DDS/install/foonathan_memory_vendor/bin:/home/<USER>/.local/bin:/home/<USER>/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin', 'SESSION_MANAGER': 'local/duythien-Ubuntu:@/tmp/.ICE-unix/2858,unix/duythien-Ubuntu:/tmp/.ICE-unix/2858', 'INVOCATION_ID': 'e212494725734083ac7a0695a3fbdefd', 'CROSS_COMPILE_64': '/home/<USER>/ti_sdk/linux-devkit/sysroots/x86_64-arago-linux/usr/bin/aarch64-oe-linux/aarch64-oe-linux-', 'PAPERSIZE': 'a4', 'XDG_MENU_PREFIX': 'gnome-', 'LC_ADDRESS': 'vi_VN.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'GDK_BACKEND': 'x11', 'DISPLAY': ':1', 'LANG': 'en_US.UTF-8', 'XDG_CURRENT_DESKTOP': 'Unity', 'LC_TELEPHONE': 'vi_VN.UTF-8', 'TILIX_ID': '7576f55f-ecf4-429b-b4a2-30b035c92f0c', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/gdm/Xauthority', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-fa7e2e94aa.sock', 'TERM_PROGRAM': 'vscode', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/opt/ros/kilted:/opt/ros/jazzy', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'SHELL': '/bin/bash', 'LC_NAME': 'vi_VN.UTF-8', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'LC_MEASUREMENT': 'vi_VN.UTF-8', 'GPG_AGENT_INFO': '/run/user/1000/gnupg/S.gpg-agent:0:1', 'LC_IDENTIFICATION': 'vi_VN.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator', 'LC_ALL': 'en_US.UTF-8', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'DBUS_STARTER_ADDRESS': 'unix:path=/run/user/1000/bus,guid=13c8833733f832d27a7c73e9687f00e8', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'QTWEBENGINE_DICTIONARIES_PATH': '/usr/share/hunspell-bdic/', 'PYTHONPATH': '/opt/ros/kilted/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'vi_VN.UTF-8', 'LC_PAPER': 'vi_VN.UTF-8', 'COLCON': '1', 'MEMORY_PRESSURE_WRITE': 'c29tZSAyMDAwMDAgMjAwMDAwMAA=', 'VTE_VERSION': '7600', 'CMAKE_PREFIX_PATH': '/opt/ros/kilted/opt/gz_math_vendor:/opt/ros/kilted/opt/gz_utils_vendor:/opt/ros/kilted/opt/gz_cmake_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/Fast-DDS/install/fastdds:/home/<USER>/Fast-DDS/install/foonathan_memory_vendor:/home/<USER>/Fast-DDS/install/fastcdr:/opt/ros/kilted:/opt/ros/jazzy'}), 'shell': False}
[0.066355] (my_calculator) StdoutLine: {'line': b'[  1%] Built target my_calculator__rosidl_generator_type_description\n'}
[0.077097] (my_calculator) StdoutLine: {'line': b'[  4%] \x1b[32mBuilding CXX object CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o\x1b[0m\n'}
[0.077365] (my_calculator) StdoutLine: {'line': b'[  4%] \x1b[32mBuilding CXX object CMakeFiles/arithmetic_server.dir/src/arithmetic_server.cpp.o\x1b[0m\n'}
[0.077535] (my_calculator) StdoutLine: {'line': b'[  4%] Built target ament_cmake_python_copy_my_calculator\n'}
[0.088456] (my_calculator) StdoutLine: {'line': b'[  5%] Built target my_calculator__cpp\n'}
[0.092604] (my_calculator) StdoutLine: {'line': b'[ 26%] Built target my_calculator__rosidl_generator_c\n'}
[0.102455] (-) TimerEvent: {}
[0.132460] (my_calculator) StdoutLine: {'line': b'[ 35%] Built target my_calculator__rosidl_typesupport_introspection_c\n'}
[0.134716] (my_calculator) StdoutLine: {'line': b'[ 48%] Built target my_calculator__rosidl_typesupport_cpp\n'}
[0.135779] (my_calculator) StdoutLine: {'line': b'[ 66%] Built target my_calculator__rosidl_typesupport_fastrtps_cpp\n'}
[0.136007] (my_calculator) StdoutLine: {'line': b'[ 75%] Built target my_calculator__rosidl_typesupport_introspection_cpp\n'}
[0.141496] (my_calculator) StdoutLine: {'line': b'[ 76%] Built target my_calculator__rosidl_typesupport_c\n'}
[0.141742] (my_calculator) StdoutLine: {'line': b'[ 79%] Built target my_calculator__rosidl_typesupport_fastrtps_c\n'}
[0.180435] (my_calculator) StdoutLine: {'line': b'[ 79%] Built target my_calculator\n'}
[0.202555] (-) TimerEvent: {}
[0.216777] (my_calculator) StdoutLine: {'line': b'[ 80%] Built target my_calculator__py\n'}
[0.273603] (my_calculator) StdoutLine: {'line': b'[ 88%] Built target my_calculator__rosidl_generator_py\n'}
[0.302674] (-) TimerEvent: {}
[0.315672] (my_calculator) StdoutLine: {'line': b'[ 91%] Built target my_calculator_s__rosidl_typesupport_fastrtps_c\n'}
[0.322498] (my_calculator) StdoutLine: {'line': b'[ 94%] Built target my_calculator_s__rosidl_typesupport_introspection_c\n'}
[0.326968] (my_calculator) StdoutLine: {'line': b'[ 97%] Built target my_calculator_s__rosidl_typesupport_c\n'}
[0.402801] (-) TimerEvent: {}
[0.460087] (my_calculator) StdoutLine: {'line': b'running egg_info\n'}
[0.490787] (my_calculator) StdoutLine: {'line': b'writing my_calculator.egg-info/PKG-INFO\n'}
[0.491080] (my_calculator) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/ROS2_WS/my_calculator/src/../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/divide.hpp:10\x1b[m\x1b[K,\n'}
[0.491211] (my_calculator) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/ROS2_WS/my_calculator/src/arithmetic_server.cpp:5\x1b[m\x1b[K:\n'}
[0.491289] (my_calculator) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ROS2_WS/my_calculator/src/../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/./detail/divide__type_support.hpp:10:10:\x1b[m\x1b[K \x1b[01;31m\x1b[Kfatal error: \x1b[m\x1b[Kmy_calculator/build/my_calculator/rosidl_generator_cpp/my_calculator/msg/rosidl_generator_cpp__visibility_control.hpp: No such file or directory\n'}
[0.491400] (my_calculator) StderrLine: {'line': b'   10 | #include \x1b[01;31m\x1b[K"my_calculator/build/my_calculator/rosidl_generator_cpp/my_calculator/msg/rosidl_generator_cpp__visibility_control.hpp"\x1b[m\x1b[K\n'}
[0.491484] (my_calculator) StderrLine: {'line': b'      |          \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[0.491556] (my_calculator) StderrLine: {'line': b'compilation terminated.\n'}
[0.491627] (my_calculator) StdoutLine: {'line': b'writing dependency_links to my_calculator.egg-info/dependency_links.txt\n'}
[0.491701] (my_calculator) StdoutLine: {'line': b'writing top-level names to my_calculator.egg-info/top_level.txt\n'}
[0.494293] (my_calculator) StderrLine: {'line': b'gmake[2]: *** [CMakeFiles/arithmetic_server.dir/build.make:76: CMakeFiles/arithmetic_server.dir/src/arithmetic_server.cpp.o] Error 1\n'}
[0.494598] (my_calculator) StderrLine: {'line': b'gmake[1]: *** [CMakeFiles/Makefile2:625: CMakeFiles/arithmetic_server.dir/all] Error 2\n'}
[0.494719] (my_calculator) StderrLine: {'line': b'gmake[1]: *** Waiting for unfinished jobs....\n'}
[0.502894] (-) TimerEvent: {}
[0.508034] (my_calculator) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/ROS2_WS/my_calculator/src/../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/divide.hpp:10\x1b[m\x1b[K,\n'}
[0.508185] (my_calculator) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/ROS2_WS/my_calculator/src/arithmetic_client.cpp:5\x1b[m\x1b[K:\n'}
[0.508260] (my_calculator) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/ROS2_WS/my_calculator/src/../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/./detail/divide__type_support.hpp:10:10:\x1b[m\x1b[K \x1b[01;31m\x1b[Kfatal error: \x1b[m\x1b[Kmy_calculator/build/my_calculator/rosidl_generator_cpp/my_calculator/msg/rosidl_generator_cpp__visibility_control.hpp: No such file or directory\n'}
[0.508355] (my_calculator) StderrLine: {'line': b'   10 | #include \x1b[01;31m\x1b[K"my_calculator/build/my_calculator/rosidl_generator_cpp/my_calculator/msg/rosidl_generator_cpp__visibility_control.hpp"\x1b[m\x1b[K\n'}
[0.508442] (my_calculator) StderrLine: {'line': b'      |          \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[0.508513] (my_calculator) StderrLine: {'line': b'compilation terminated.\n'}
[0.511098] (my_calculator) StderrLine: {'line': b'gmake[2]: *** [CMakeFiles/arithmetic_client.dir/build.make:76: CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o] Error 1\n'}
[0.511280] (my_calculator) StderrLine: {'line': b'gmake[1]: *** [CMakeFiles/Makefile2:651: CMakeFiles/arithmetic_client.dir/all] Error 2\n'}
[0.567230] (my_calculator) StdoutLine: {'line': b"reading manifest file 'my_calculator.egg-info/SOURCES.txt'\n"}
[0.567840] (my_calculator) StdoutLine: {'line': b"writing manifest file 'my_calculator.egg-info/SOURCES.txt'\n"}
[0.596252] (my_calculator) StdoutLine: {'line': b'[ 97%] Built target ament_cmake_python_build_my_calculator_egg\n'}
[0.597480] (my_calculator) StderrLine: {'line': b'gmake: *** [Makefile:146: all] Error 2\n'}
[0.599294] (my_calculator) CommandEnded: {'returncode': 2}
[0.603021] (-) TimerEvent: {}
[0.703277] (-) TimerEvent: {}
[0.755566] (my_calculator) JobEnded: {'identifier': 'my_calculator', 'rc': 2}
[0.756500] (-) EventReactorShutdown: {}
