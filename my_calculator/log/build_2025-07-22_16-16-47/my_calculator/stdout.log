-- Found ament_cmake: 2.7.3 (/opt/ros/kilted/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter 
-- Found rclcpp: 29.5.0 (/opt/ros/kilted/share/rclcpp/cmake)
-- Found rosidl_generator_c: 4.9.4 (/opt/ros/kilted/share/rosidl_generator_c/cmake)
-- Found rosidl_generator_cpp: 4.9.4 (/opt/ros/kilted/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 7.8.2 (/opt/ros/kilted/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 9.3.2 (/opt/ros/kilted/share/rmw_fastrtps_cpp/cmake)
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found rosidl_default_generators: 1.7.1 (/opt/ros/kilted/share/rosidl_default_generators/cmake)
-- Found rosidl_adapter: 4.9.4 (/opt/ros/kilted/share/rosidl_adapter/cmake)
-- Found ament_cmake_ros_core: 0.14.3 (/opt/ros/kilted/share/ament_cmake_ros_core/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter Development NumPy Development.Module Development.Embed 
-- Configuring done (1.2s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/ROS2_WS/my_calculator/build/my_calculator
[  2%] [34m[1mGenerating type hashes for ROS interfaces[0m
[  2%] [32mBuilding CXX object CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o[0m
[  4%] [32mBuilding CXX object CMakeFiles/arithmetic_server.dir/src/arithmetic_server.cpp.o[0m
[  4%] Built target ament_cmake_python_copy_my_calculator
[  4%] Built target my_calculator__rosidl_generator_type_description
[  5%] [34m[1mGenerating C code for ROS interfaces[0m
running egg_info
creating my_calculator.egg-info
[  7%] [34m[1mGenerating C++ code for ROS interfaces[0m
writing my_calculator.egg-info/PKG-INFO
writing dependency_links to my_calculator.egg-info/dependency_links.txt
writing top-level names to my_calculator.egg-info/top_level.txt
writing manifest file 'my_calculator.egg-info/SOURCES.txt'
reading manifest file 'my_calculator.egg-info/SOURCES.txt'
writing manifest file 'my_calculator.egg-info/SOURCES.txt'
[  7%] Built target ament_cmake_python_build_my_calculator_egg
[  7%] Built target my_calculator__cpp
[ 10%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_c.dir/rosidl_generator_c/my_calculator/srv/detail/add__description.c.o[0m
[ 10%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_c.dir/rosidl_generator_c/my_calculator/srv/detail/add__type_support.c.o[0m
[ 14%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_c.dir/rosidl_generator_c/my_calculator/srv/detail/subtract__functions.c.o[0m
[ 14%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_c.dir/rosidl_generator_c/my_calculator/srv/detail/add__functions.c.o[0m
[ 16%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_c.dir/rosidl_generator_c/my_calculator/srv/detail/multiply__functions.c.o[0m
[ 17%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_c.dir/rosidl_generator_c/my_calculator/srv/detail/subtract__description.c.o[0m
[ 19%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_c.dir/rosidl_generator_c/my_calculator/srv/detail/subtract__type_support.c.o[0m
[ 19%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_c.dir/rosidl_generator_c/my_calculator/srv/detail/multiply__type_support.c.o[0m
[ 20%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_c.dir/rosidl_generator_c/my_calculator/srv/detail/multiply__description.c.o[0m
[ 22%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_c.dir/rosidl_generator_c/my_calculator/srv/detail/divide__description.c.o[0m
[ 23%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_c.dir/rosidl_generator_c/my_calculator/srv/detail/divide__functions.c.o[0m
[ 25%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_c.dir/rosidl_generator_c/my_calculator/srv/detail/divide__type_support.c.o[0m
[ 26%] [32m[1mLinking C shared library libmy_calculator__rosidl_generator_c.so[0m
[ 26%] Built target my_calculator__rosidl_generator_c
