[0.011s] Invoking command in '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/kilted:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/ROS2_WS/my_calculator -DCMAKE_INSTALL_PREFIX=/home/<USER>/ROS2_WS/my_calculator/install/my_calculator
[0.025s] -- Found ament_cmake: 2.7.3 (/opt/ros/kilted/share/ament_cmake/cmake)
[0.053s] -- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter 
[0.133s] -- Found rclcpp: 29.5.0 (/opt/ros/kilted/share/rclcpp/cmake)
[0.161s] -- Found rosidl_generator_c: 4.9.4 (/opt/ros/kilted/share/rosidl_generator_c/cmake)
[0.170s] -- Found rosidl_generator_cpp: 4.9.4 (/opt/ros/kilted/share/rosidl_generator_cpp/cmake)
[0.182s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.196s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.261s] -- Found rmw_implementation_cmake: 7.8.2 (/opt/ros/kilted/share/rmw_implementation_cmake/cmake)
[0.262s] -- Found rmw_fastrtps_cpp: 9.3.2 (/opt/ros/kilted/share/rmw_fastrtps_cpp/cmake)
[0.301s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.374s] -- Found rosidl_default_generators: 1.7.1 (/opt/ros/kilted/share/rosidl_default_generators/cmake)
[0.382s] -- Found rosidl_adapter: 4.9.4 (/opt/ros/kilted/share/rosidl_adapter/cmake)
[0.663s] -- Found ament_cmake_ros_core: 0.14.3 (/opt/ros/kilted/share/ament_cmake_ros_core/cmake)
[0.826s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.896s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[1.159s] -- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter Development NumPy Development.Module Development.Embed 
[1.177s] -- Configuring done (1.2s)
[1.237s] -- Generating done (0.0s)
[1.241s] -- Build files have been written to: /home/<USER>/ROS2_WS/my_calculator/build/my_calculator
[1.249s] Invoked command in '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/kilted:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/ROS2_WS/my_calculator -DCMAKE_INSTALL_PREFIX=/home/<USER>/ROS2_WS/my_calculator/install/my_calculator
[1.251s] Invoking command in '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/kilted:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ROS2_WS/my_calculator/build/my_calculator -- -j12 -l12
[1.304s] [  2%] [34m[1mGenerating type hashes for ROS interfaces[0m
[1.304s] [  2%] [32mBuilding CXX object CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o[0m
[1.304s] [  4%] [32mBuilding CXX object CMakeFiles/arithmetic_server.dir/src/arithmetic_server.cpp.o[0m
[1.313s] [  4%] Built target ament_cmake_python_copy_my_calculator
[1.599s] [  4%] Built target my_calculator__rosidl_generator_type_description
[1.614s] [  5%] [34m[1mGenerating C code for ROS interfaces[0m
[1.625s] running egg_info
[1.626s] creating my_calculator.egg-info
[1.632s] [  7%] [34m[1mGenerating C++ code for ROS interfaces[0m
[1.675s] writing my_calculator.egg-info/PKG-INFO
[1.675s] writing dependency_links to my_calculator.egg-info/dependency_links.txt
[1.676s] writing top-level names to my_calculator.egg-info/top_level.txt
[1.676s] writing manifest file 'my_calculator.egg-info/SOURCES.txt'
[1.690s] [01m[K/home/<USER>/ROS2_WS/my_calculator/src/arithmetic_client.cpp:2:10:[m[K [01;31m[Kfatal error: [m[Kmy_calculator/srv/add.hpp: No such file or directory
[1.690s]     2 | #include [01;31m[K<my_calculator/srv/add.hpp>[m[K
[1.690s]       |          [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[1.690s] compilation terminated.
[1.693s] gmake[2]: *** [CMakeFiles/arithmetic_client.dir/build.make:76: CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o] Error 1
[1.694s] gmake[1]: *** [CMakeFiles/Makefile2:651: CMakeFiles/arithmetic_client.dir/all] Error 2
[1.694s] gmake[1]: *** Waiting for unfinished jobs....
[1.698s] [01m[K/home/<USER>/ROS2_WS/my_calculator/src/arithmetic_server.cpp:2:10:[m[K [01;31m[Kfatal error: [m[Kmy_calculator/srv/add.hpp: No such file or directory
[1.698s]     2 | #include [01;31m[K<my_calculator/srv/add.hpp>[m[K
[1.699s]       |          [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[1.699s] compilation terminated.
[1.709s] gmake[2]: *** [CMakeFiles/arithmetic_server.dir/build.make:76: CMakeFiles/arithmetic_server.dir/src/arithmetic_server.cpp.o] Error 1
[1.709s] gmake[1]: *** [CMakeFiles/Makefile2:625: CMakeFiles/arithmetic_server.dir/all] Error 2
[1.798s] reading manifest file 'my_calculator.egg-info/SOURCES.txt'
[1.799s] writing manifest file 'my_calculator.egg-info/SOURCES.txt'
[1.830s] [  7%] Built target ament_cmake_python_build_my_calculator_egg
[2.221s] [  7%] Built target my_calculator__cpp
[2.357s] [ 10%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_c.dir/rosidl_generator_c/my_calculator/srv/detail/add__description.c.o[0m
[2.357s] [ 10%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_c.dir/rosidl_generator_c/my_calculator/srv/detail/add__type_support.c.o[0m
[2.357s] [ 14%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_c.dir/rosidl_generator_c/my_calculator/srv/detail/subtract__functions.c.o[0m
[2.357s] [ 14%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_c.dir/rosidl_generator_c/my_calculator/srv/detail/add__functions.c.o[0m
[2.357s] [ 16%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_c.dir/rosidl_generator_c/my_calculator/srv/detail/multiply__functions.c.o[0m
[2.358s] [ 17%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_c.dir/rosidl_generator_c/my_calculator/srv/detail/subtract__description.c.o[0m
[2.358s] [ 19%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_c.dir/rosidl_generator_c/my_calculator/srv/detail/subtract__type_support.c.o[0m
[2.358s] [ 19%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_c.dir/rosidl_generator_c/my_calculator/srv/detail/multiply__type_support.c.o[0m
[2.358s] [ 20%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_c.dir/rosidl_generator_c/my_calculator/srv/detail/multiply__description.c.o[0m
[2.358s] [ 22%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_c.dir/rosidl_generator_c/my_calculator/srv/detail/divide__description.c.o[0m
[2.359s] [ 23%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_c.dir/rosidl_generator_c/my_calculator/srv/detail/divide__functions.c.o[0m
[2.365s] [ 25%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_generator_c.dir/rosidl_generator_c/my_calculator/srv/detail/divide__type_support.c.o[0m
[2.492s] [ 26%] [32m[1mLinking C shared library libmy_calculator__rosidl_generator_c.so[0m
[2.535s] [ 26%] Built target my_calculator__rosidl_generator_c
[2.536s] gmake: *** [Makefile:146: all] Error 2
[2.539s] Invoked command in '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator' returned '2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/kilted:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ROS2_WS/my_calculator/build/my_calculator -- -j12 -l12
