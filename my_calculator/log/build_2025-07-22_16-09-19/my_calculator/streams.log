[0.011s] Invoking command in '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/kilted:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/ROS2_WS/my_calculator -DCMAKE_INSTALL_PREFIX=/home/<USER>/ROS2_WS/my_calculator/install/my_calculator
[0.025s] -- Found ament_cmake: 2.7.3 (/opt/ros/kilted/share/ament_cmake/cmake)
[0.244s] -- Found rclcpp: 29.5.0 (/opt/ros/kilted/share/rclcpp/cmake)
[0.272s] -- Found rosidl_generator_c: 4.9.4 (/opt/ros/kilted/share/rosidl_generator_c/cmake)
[0.282s] -- Found rosidl_generator_cpp: 4.9.4 (/opt/ros/kilted/share/rosidl_generator_cpp/cmake)
[0.295s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.310s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.397s] -- Found rmw_implementation_cmake: 7.8.2 (/opt/ros/kilted/share/rmw_implementation_cmake/cmake)
[0.399s] -- Found rmw_fastrtps_cpp: 9.3.2 (/opt/ros/kilted/share/rmw_fastrtps_cpp/cmake)
[0.441s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.513s] -- Found rosidl_default_generators: 1.7.1 (/opt/ros/kilted/share/rosidl_default_generators/cmake)
[0.522s] -- Found rosidl_adapter: 4.9.4 (/opt/ros/kilted/share/rosidl_adapter/cmake)
[0.524s] [31mCMake Error at /opt/ros/kilted/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake:95 (message):
[0.524s]   rosidl_generate_interfaces() the passed file 'srv/Subtract.srv' doesn't
[0.524s]   exist relative to the CMAKE_CURRENT_SOURCE_DIR
[0.524s]   '/home/<USER>/ROS2_WS/my_calculator'
[0.525s] Call Stack (most recent call first):
[0.525s]   CMakeLists.txt:19 (rosidl_generate_interfaces)
[0.525s] 
[0.525s] [0m
[0.525s] -- Configuring incomplete, errors occurred!
[0.532s] Invoked command in '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator' returned '1': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/kilted:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/ROS2_WS/my_calculator -DCMAKE_INSTALL_PREFIX=/home/<USER>/ROS2_WS/my_calculator/install/my_calculator
