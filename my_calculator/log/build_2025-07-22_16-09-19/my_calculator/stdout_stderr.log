-- Found ament_cmake: 2.7.3 (/opt/ros/kilted/share/ament_cmake/cmake)
-- Found rclcpp: 29.5.0 (/opt/ros/kilted/share/rclcpp/cmake)
-- Found rosidl_generator_c: 4.9.4 (/opt/ros/kilted/share/rosidl_generator_c/cmake)
-- Found rosidl_generator_cpp: 4.9.4 (/opt/ros/kilted/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 7.8.2 (/opt/ros/kilted/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 9.3.2 (/opt/ros/kilted/share/rmw_fastrtps_cpp/cmake)
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found rosidl_default_generators: 1.7.1 (/opt/ros/kilted/share/rosidl_default_generators/cmake)
-- Found rosidl_adapter: 4.9.4 (/opt/ros/kilted/share/rosidl_adapter/cmake)
[31mCMake Error at /opt/ros/kilted/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake:95 (message):
  rosidl_generate_interfaces() the passed file 'srv/Subtract.srv' doesn't
  exist relative to the CMAKE_CURRENT_SOURCE_DIR
  '/home/<USER>/ROS2_WS/my_calculator'
Call Stack (most recent call first):
  CMakeLists.txt:19 (rosidl_generate_interfaces)

[0m
-- Configuring incomplete, errors occurred!
