-- Found ament_cmake: 2.7.3 (/opt/ros/kilted/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter 
-- Found rclcpp: 29.5.0 (/opt/ros/kilted/share/rclcpp/cmake)
-- Found rosidl_generator_c: 4.9.4 (/opt/ros/kilted/share/rosidl_generator_c/cmake)
-- Found rosidl_generator_cpp: 4.9.4 (/opt/ros/kilted/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 7.8.2 (/opt/ros/kilted/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 9.3.2 (/opt/ros/kilted/share/rmw_fastrtps_cpp/cmake)
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found rosidl_default_generators: 1.7.1 (/opt/ros/kilted/share/rosidl_default_generators/cmake)
-- Found rosidl_adapter: 4.9.4 (/opt/ros/kilted/share/rosidl_adapter/cmake)
-- Found ament_cmake_ros_core: 0.14.3 (/opt/ros/kilted/share/ament_cmake_ros_core/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter Development NumPy Development.Module Development.Embed 
-- Configuring done (1.1s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/ROS2_WS/my_calculator/build/my_calculator
[  1%] [34m[1mGenerating type hashes for ROS interfaces[0m
[  2%] [32mBuilding CXX object CMakeFiles/arithmetic_server.dir/src/arithmetic_server.cpp.o[0m
[  4%] [32mBuilding CXX object CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o[0m
[  4%] Built target ament_cmake_python_copy_my_calculator
[  4%] Built target my_calculator__rosidl_generator_type_description
running egg_info
[01m[K/home/<USER>/ROS2_WS/my_calculator/src/arithmetic_client.cpp:2:10:[m[K [01;31m[Kfatal error: [m[K../build/my_calculator/rosidl_generator_cpp/./add.hpp: No such file or directory
    2 | #include [01;31m[K"../build/my_calculator/rosidl_generator_cpp/./add.hpp"[m[K
      |          [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
compilation terminated.
[01m[K/home/<USER>/ROS2_WS/my_calculator/src/arithmetic_server.cpp:2:10:[m[K [01;31m[Kfatal error: [m[K../build/my_calculator/rosidl_generator_cpp/./add.hpp: No such file or directory
    2 | #include [01;31m[K"../build/my_calculator/rosidl_generator_cpp/./add.hpp"[m[K
      |          [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
compilation terminated.
gmake[2]: *** [CMakeFiles/arithmetic_client.dir/build.make:76: CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o] Error 1
gmake[1]: *** [CMakeFiles/Makefile2:651: CMakeFiles/arithmetic_client.dir/all] Error 2
gmake[1]: *** Waiting for unfinished jobs....
gmake[2]: *** [CMakeFiles/arithmetic_server.dir/build.make:76: CMakeFiles/arithmetic_server.dir/src/arithmetic_server.cpp.o] Error 1
gmake[1]: *** [CMakeFiles/Makefile2:625: CMakeFiles/arithmetic_server.dir/all] Error 2
[  5%] [34m[1mGenerating C code for ROS interfaces[0m
[  7%] [34m[1mGenerating C++ code for ROS interfaces[0m
writing my_calculator.egg-info/PKG-INFO
writing dependency_links to my_calculator.egg-info/dependency_links.txt
writing top-level names to my_calculator.egg-info/top_level.txt
reading manifest file 'my_calculator.egg-info/SOURCES.txt'
writing manifest file 'my_calculator.egg-info/SOURCES.txt'
[  7%] Built target ament_cmake_python_build_my_calculator_egg
