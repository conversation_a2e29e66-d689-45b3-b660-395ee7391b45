[0.010s] Invoking command in '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/kilted:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ROS2_WS/my_calculator/build/my_calculator -- -j12 -l12
[0.039s] -- Found ament_cmake: 2.7.3 (/opt/ros/kilted/share/ament_cmake/cmake)
[0.066s] -- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter 
[0.146s] -- Found rclcpp: 29.5.0 (/opt/ros/kilted/share/rclcpp/cmake)
[0.173s] -- Found rosidl_generator_c: 4.9.4 (/opt/ros/kilted/share/rosidl_generator_c/cmake)
[0.183s] -- Found rosidl_generator_cpp: 4.9.4 (/opt/ros/kilted/share/rosidl_generator_cpp/cmake)
[0.195s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.210s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.276s] -- Found rmw_implementation_cmake: 7.8.2 (/opt/ros/kilted/share/rmw_implementation_cmake/cmake)
[0.277s] -- Found rmw_fastrtps_cpp: 9.3.2 (/opt/ros/kilted/share/rmw_fastrtps_cpp/cmake)
[0.318s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.398s] -- Found rosidl_default_generators: 1.7.1 (/opt/ros/kilted/share/rosidl_default_generators/cmake)
[0.407s] -- Found rosidl_adapter: 4.9.4 (/opt/ros/kilted/share/rosidl_adapter/cmake)
[0.680s] -- Found ament_cmake_ros_core: 0.14.3 (/opt/ros/kilted/share/ament_cmake_ros_core/cmake)
[0.836s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.894s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[1.127s] -- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter Development NumPy Development.Module Development.Embed 
[1.144s] -- Configuring done (1.1s)
[1.194s] -- Generating done (0.0s)
[1.198s] -- Build files have been written to: /home/<USER>/ROS2_WS/my_calculator/build/my_calculator
[1.237s] [  1%] [34m[1mGenerating type hashes for ROS interfaces[0m
[1.238s] [  2%] [32mBuilding CXX object CMakeFiles/arithmetic_server.dir/src/arithmetic_server.cpp.o[0m
[1.238s] [  4%] [32mBuilding CXX object CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o[0m
[1.249s] [  4%] Built target ament_cmake_python_copy_my_calculator
[1.542s] [  4%] Built target my_calculator__rosidl_generator_type_description
[1.546s] running egg_info
[1.552s] [01m[K/home/<USER>/ROS2_WS/my_calculator/src/arithmetic_client.cpp:2:10:[m[K [01;31m[Kfatal error: [m[K../build/my_calculator/rosidl_generator_cpp/./add.hpp: No such file or directory
[1.552s]     2 | #include [01;31m[K"../build/my_calculator/rosidl_generator_cpp/./add.hpp"[m[K
[1.552s]       |          [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[1.552s] compilation terminated.
[1.552s] [01m[K/home/<USER>/ROS2_WS/my_calculator/src/arithmetic_server.cpp:2:10:[m[K [01;31m[Kfatal error: [m[K../build/my_calculator/rosidl_generator_cpp/./add.hpp: No such file or directory
[1.552s]     2 | #include [01;31m[K"../build/my_calculator/rosidl_generator_cpp/./add.hpp"[m[K
[1.552s]       |          [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[1.553s] compilation terminated.
[1.555s] gmake[2]: *** [CMakeFiles/arithmetic_client.dir/build.make:76: CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o] Error 1
[1.555s] gmake[1]: *** [CMakeFiles/Makefile2:651: CMakeFiles/arithmetic_client.dir/all] Error 2
[1.555s] gmake[1]: *** Waiting for unfinished jobs....
[1.555s] gmake[2]: *** [CMakeFiles/arithmetic_server.dir/build.make:76: CMakeFiles/arithmetic_server.dir/src/arithmetic_server.cpp.o] Error 1
[1.555s] gmake[1]: *** [CMakeFiles/Makefile2:625: CMakeFiles/arithmetic_server.dir/all] Error 2
[1.558s] [  5%] [34m[1mGenerating C code for ROS interfaces[0m
[1.568s] [  7%] [34m[1mGenerating C++ code for ROS interfaces[0m
[1.572s] writing my_calculator.egg-info/PKG-INFO
[1.573s] writing dependency_links to my_calculator.egg-info/dependency_links.txt
[1.573s] writing top-level names to my_calculator.egg-info/top_level.txt
[1.647s] reading manifest file 'my_calculator.egg-info/SOURCES.txt'
[1.647s] writing manifest file 'my_calculator.egg-info/SOURCES.txt'
[1.676s] [  7%] Built target ament_cmake_python_build_my_calculator_egg
