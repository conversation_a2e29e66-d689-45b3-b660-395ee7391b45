[0.012s] Invoking command in '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/kilted:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/ROS2_WS/my_calculator -DCMAKE_INSTALL_PREFIX=/home/<USER>/ROS2_WS/my_calculator/install/my_calculator
[0.027s] -- Found ament_cmake: 2.7.3 (/opt/ros/kilted/share/ament_cmake/cmake)
[0.246s] -- Found rclcpp: 29.5.0 (/opt/ros/kilted/share/rclcpp/cmake)
[0.274s] -- Found rosidl_generator_c: 4.9.4 (/opt/ros/kilted/share/rosidl_generator_c/cmake)
[0.285s] -- Found rosidl_generator_cpp: 4.9.4 (/opt/ros/kilted/share/rosidl_generator_cpp/cmake)
[0.297s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.312s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.379s] -- Found rmw_implementation_cmake: 7.8.2 (/opt/ros/kilted/share/rmw_implementation_cmake/cmake)
[0.380s] -- Found rmw_fastrtps_cpp: 9.3.2 (/opt/ros/kilted/share/rmw_fastrtps_cpp/cmake)
[0.423s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.496s] -- Found rosidl_default_generators: 1.7.1 (/opt/ros/kilted/share/rosidl_default_generators/cmake)
[0.504s] -- Found rosidl_adapter: 4.9.4 (/opt/ros/kilted/share/rosidl_adapter/cmake)
[1.131s] -- Found ament_cmake_ros_core: 0.14.3 (/opt/ros/kilted/share/ament_cmake_ros_core/cmake)
[1.822s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[2.105s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[2.559s] -- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter Development NumPy Development.Module Development.Embed 
[2.592s] [0mCMake Deprecation Warning at /opt/ros/kilted/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake:87 (message):
[2.593s]   ament_target_dependencies() is deprecated.  Use target_link_libraries()
[2.593s]   with modern CMake targets instead.  Try replacing this call with:
[2.593s] 
[2.593s]     target_link_libraries(arithmetic_server PUBLIC
[2.593s]       rclcpp::rclcpp
[2.593s]     )
[2.593s] 
[2.593s] Call Stack (most recent call first):
[2.593s]   CMakeLists.txt:29 (ament_target_dependencies)
[2.594s] 
[2.594s] [0m
[2.594s] [31mCMake Error at /opt/ros/kilted/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake:101 (message):
[2.594s]   ament_target_dependencies() the passed package name
[2.594s]   'my_calculator_interfaces' was not found before
[2.594s] Call Stack (most recent call first):
[2.594s]   CMakeLists.txt:29 (ament_target_dependencies)
[2.594s] 
[2.594s] [0m
[2.594s] -- Configuring incomplete, errors occurred!
[2.601s] Invoked command in '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator' returned '1': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/kilted:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/ROS2_WS/my_calculator -DCMAKE_INSTALL_PREFIX=/home/<USER>/ROS2_WS/my_calculator/install/my_calculator
