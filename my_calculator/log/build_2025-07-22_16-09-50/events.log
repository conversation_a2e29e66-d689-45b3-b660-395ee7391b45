[0.000000] (-) TimerEvent: {}
[0.000551] (my_calculator) JobQueued: {'identifier': 'my_calculator', 'dependencies': OrderedDict()}
[0.001080] (my_calculator) JobStarted: {'identifier': 'my_calculator'}
[0.009766] (my_calculator) JobProgress: {'identifier': 'my_calculator', 'progress': 'cmake'}
[0.011012] (my_calculator) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/ROS2_WS/my_calculator', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/ROS2_WS/my_calculator/install/my_calculator'], 'cwd': '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator', 'env': OrderedDict({'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'en_AU:en', 'SYSROOT_64': '/home/<USER>/ti_sdk/linux-devkit/sysroots/aarch64-oe-linux', 'USER': 'duythien', 'LC_TIME': 'vi_VN.UTF-8', 'CC_64': '/home/<USER>/ti_sdk/linux-devkit/sysroots/x86_64-arago-linux/usr/bin/aarch64-oe-linux/aarch64-oe-linux-gcc --sysroot=/home/<USER>/ti_sdk/linux-devkit/sysroots/aarch64-oe-linux', 'XDG_SESSION_TYPE': 'x11', 'GIT_ASKPASS': '/usr/share/code/resources/app/extensions/git/dist/askpass.sh', 'ROS_APT_SOURCE_VERSION': '1.1.0', 'SHLVL': '2', 'LD_LIBRARY_PATH': '/opt/ros/kilted/opt/zenoh_cpp_vendor/lib:/opt/ros/kilted/opt/gz_math_vendor/lib:/opt/ros/kilted/opt/gz_utils_vendor/lib:/opt/ros/kilted/opt/rviz_ogre_vendor/lib:/opt/ros/kilted/lib/x86_64-linux-gnu:/opt/ros/kilted/opt/gz_cmake_vendor/lib:/opt/ros/kilted/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib:/home/<USER>/Fast-DDS/install/fastdds/lib:/home/<USER>/Fast-DDS/install/fastcdr/lib', 'HOME': '/home/<USER>', 'CHROME_DESKTOP': 'code.desktop', 'OLDPWD': '/home/<USER>/ROS2_WS', 'TERM_PROGRAM_VERSION': '1.95.1', 'DESKTOP_SESSION': 'ubuntu', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'VSCODE_GIT_ASKPASS_MAIN': '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js', 'LC_MONETARY': 'vi_VN.UTF-8', 'VSCODE_GIT_ASKPASS_NODE': '/usr/share/code/code', 'MANAGERPID': '2429', 'DBUS_STARTER_BUS_TYPE': 'session', 'SYSTEMD_EXEC_PID': '2858', 'GSM_SKIP_SSH_AGENT_WORKAROUND': 'true', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus,guid=13c8833733f832d27a7c73e9687f00e8', 'COLORTERM': 'truecolor', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'COLCON_PREFIX_PATH': '/home/<USER>/Fast-DDS/install', 'ROS_DISTRO': 'kilted', 'LOGNAME': 'duythien', 'CROSS_COMPILE_32': '/home/<USER>/ti_sdk/k3r5-devkit/sysroots/x86_64-arago-linux/usr/bin/arm-oe-eabi/arm-oe-eabi-', 'JOURNAL_STREAM': '9:16178', '_': '/home/<USER>/.local/bin/colcon', 'ROS_VERSION': '2', 'PKG_CONFIG_PATH': '/home/<USER>/Fast-DDS/install/foonathan_memory_vendor/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/Fast-DDS/install/foonathan_memory_vendor/lib/pkgconfig', 'MEMORY_PRESSURE_WATCH': '/sys/fs/cgroup/user.slice/user-1000.slice/<EMAIL>/app.slice/app-gnome\\x2dsession\\x2dmanager.slice/<EMAIL>/memory.pressure', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'duythien', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'WINDOWPATH': '2', 'PATH': '/opt/ros/kilted/bin:/opt/ros/jazzy/bin:/home/<USER>/Fast-DDS/install/fastdds/bin:/home/<USER>/Fast-DDS/install/foonathan_memory_vendor/bin:/home/<USER>/.local/bin:/home/<USER>/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin', 'SESSION_MANAGER': 'local/duythien-Ubuntu:@/tmp/.ICE-unix/2858,unix/duythien-Ubuntu:/tmp/.ICE-unix/2858', 'INVOCATION_ID': 'e212494725734083ac7a0695a3fbdefd', 'CROSS_COMPILE_64': '/home/<USER>/ti_sdk/linux-devkit/sysroots/x86_64-arago-linux/usr/bin/aarch64-oe-linux/aarch64-oe-linux-', 'PAPERSIZE': 'a4', 'XDG_MENU_PREFIX': 'gnome-', 'LC_ADDRESS': 'vi_VN.UTF-8', 'XDG_RUNTIME_DIR': '/run/user/1000', 'GDK_BACKEND': 'x11', 'DISPLAY': ':1', 'LANG': 'en_US.UTF-8', 'XDG_CURRENT_DESKTOP': 'Unity', 'LC_TELEPHONE': 'vi_VN.UTF-8', 'TILIX_ID': '7576f55f-ecf4-429b-b4a2-30b035c92f0c', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/gdm/Xauthority', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-fa7e2e94aa.sock', 'TERM_PROGRAM': 'vscode', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/opt/ros/kilted:/opt/ros/jazzy', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'SHELL': '/bin/bash', 'LC_NAME': 'vi_VN.UTF-8', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'LC_MEASUREMENT': 'vi_VN.UTF-8', 'GPG_AGENT_INFO': '/run/user/1000/gnupg/S.gpg-agent:0:1', 'LC_IDENTIFICATION': 'vi_VN.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator', 'LC_ALL': 'en_US.UTF-8', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'DBUS_STARTER_ADDRESS': 'unix:path=/run/user/1000/bus,guid=13c8833733f832d27a7c73e9687f00e8', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'QTWEBENGINE_DICTIONARIES_PATH': '/usr/share/hunspell-bdic/', 'PYTHONPATH': '/opt/ros/kilted/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'vi_VN.UTF-8', 'LC_PAPER': 'vi_VN.UTF-8', 'COLCON': '1', 'MEMORY_PRESSURE_WRITE': 'c29tZSAyMDAwMDAgMjAwMDAwMAA=', 'VTE_VERSION': '7600', 'CMAKE_PREFIX_PATH': '/opt/ros/kilted/opt/gz_math_vendor:/opt/ros/kilted/opt/gz_utils_vendor:/opt/ros/kilted/opt/gz_cmake_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/Fast-DDS/install/fastdds:/home/<USER>/Fast-DDS/install/foonathan_memory_vendor:/home/<USER>/Fast-DDS/install/fastcdr:/opt/ros/kilted:/opt/ros/jazzy'}), 'shell': False}
[0.027663] (my_calculator) StdoutLine: {'line': b'-- Found ament_cmake: 2.7.3 (/opt/ros/kilted/share/ament_cmake/cmake)\n'}
[0.099787] (-) TimerEvent: {}
[0.200022] (-) TimerEvent: {}
[0.247381] (my_calculator) StdoutLine: {'line': b'-- Found rclcpp: 29.5.0 (/opt/ros/kilted/share/rclcpp/cmake)\n'}
[0.275106] (my_calculator) StdoutLine: {'line': b'-- Found rosidl_generator_c: 4.9.4 (/opt/ros/kilted/share/rosidl_generator_c/cmake)\n'}
[0.285589] (my_calculator) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 4.9.4 (/opt/ros/kilted/share/rosidl_generator_cpp/cmake)\n'}
[0.298343] (my_calculator) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.300122] (-) TimerEvent: {}
[0.313238] (my_calculator) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.380003] (my_calculator) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 7.8.2 (/opt/ros/kilted/share/rmw_implementation_cmake/cmake)\n'}
[0.381498] (my_calculator) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 9.3.2 (/opt/ros/kilted/share/rmw_fastrtps_cpp/cmake)\n'}
[0.400253] (-) TimerEvent: {}
[0.423680] (my_calculator) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[0.496588] (my_calculator) StdoutLine: {'line': b'-- Found rosidl_default_generators: 1.7.1 (/opt/ros/kilted/share/rosidl_default_generators/cmake)\n'}
[0.500372] (-) TimerEvent: {}
[0.505241] (my_calculator) StdoutLine: {'line': b'-- Found rosidl_adapter: 4.9.4 (/opt/ros/kilted/share/rosidl_adapter/cmake)\n'}
[0.600497] (-) TimerEvent: {}
[0.700693] (-) TimerEvent: {}
[0.800918] (-) TimerEvent: {}
[0.901147] (-) TimerEvent: {}
[1.001375] (-) TimerEvent: {}
[1.101577] (-) TimerEvent: {}
[1.132343] (my_calculator) StdoutLine: {'line': b'-- Found ament_cmake_ros_core: 0.14.3 (/opt/ros/kilted/share/ament_cmake_ros_core/cmake)\n'}
[1.201659] (-) TimerEvent: {}
[1.301886] (-) TimerEvent: {}
[1.402113] (-) TimerEvent: {}
[1.502344] (-) TimerEvent: {}
[1.602515] (-) TimerEvent: {}
[1.702731] (-) TimerEvent: {}
[1.802957] (-) TimerEvent: {}
[1.823404] (my_calculator) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[1.903077] (-) TimerEvent: {}
[2.003307] (-) TimerEvent: {}
[2.103505] (-) TimerEvent: {}
[2.105837] (my_calculator) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[2.203622] (-) TimerEvent: {}
[2.303842] (-) TimerEvent: {}
[2.404065] (-) TimerEvent: {}
[2.504293] (-) TimerEvent: {}
[2.559708] (my_calculator) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter Development NumPy Development.Module Development.Embed \n'}
[2.593373] (my_calculator) StderrLine: {'line': b'\x1b[0mCMake Deprecation Warning at /opt/ros/kilted/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake:87 (message):\n'}
[2.593568] (my_calculator) StderrLine: {'line': b'  ament_target_dependencies() is deprecated.  Use target_link_libraries()\n'}
[2.593695] (my_calculator) StderrLine: {'line': b'  with modern CMake targets instead.  Try replacing this call with:\n'}
[2.593820] (my_calculator) StderrLine: {'line': b'\n'}
[2.593974] (my_calculator) StderrLine: {'line': b'    target_link_libraries(arithmetic_server PUBLIC\n'}
[2.594046] (my_calculator) StderrLine: {'line': b'      rclcpp::rclcpp\n'}
[2.594112] (my_calculator) StderrLine: {'line': b'    )\n'}
[2.594176] (my_calculator) StderrLine: {'line': b'\n'}
[2.594282] (my_calculator) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[2.594398] (my_calculator) StderrLine: {'line': b'  CMakeLists.txt:29 (ament_target_dependencies)\n'}
[2.594528] (my_calculator) StderrLine: {'line': b'\n'}
[2.594677] (my_calculator) StderrLine: {'line': b'\x1b[0m\n'}
[2.594816] (my_calculator) StderrLine: {'line': b'\x1b[31mCMake Error at /opt/ros/kilted/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake:101 (message):\n'}
[2.594930] (my_calculator) StderrLine: {'line': b'  ament_target_dependencies() the passed package name\n'}
[2.594999] (my_calculator) StderrLine: {'line': b"  'my_calculator_interfaces' was not found before\n"}
[2.595064] (my_calculator) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[2.595138] (my_calculator) StderrLine: {'line': b'  CMakeLists.txt:29 (ament_target_dependencies)\n'}
[2.595200] (my_calculator) StderrLine: {'line': b'\n'}
[2.595263] (my_calculator) StderrLine: {'line': b'\x1b[0m\n'}
[2.595326] (my_calculator) StdoutLine: {'line': b'-- Configuring incomplete, errors occurred!\n'}
[2.602298] (my_calculator) CommandEnded: {'returncode': 1}
[2.604395] (-) TimerEvent: {}
[2.620967] (my_calculator) JobEnded: {'identifier': 'my_calculator', 'rc': 1}
[2.621853] (-) EventReactorShutdown: {}
