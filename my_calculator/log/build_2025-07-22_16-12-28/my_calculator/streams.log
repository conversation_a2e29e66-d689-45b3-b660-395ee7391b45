[0.009s] Invoking command in '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/kilted:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/ROS2_WS/my_calculator -DCMAKE_INSTALL_PREFIX=/home/<USER>/ROS2_WS/my_calculator/install/my_calculator
[0.023s] -- Found ament_cmake: 2.7.3 (/opt/ros/kilted/share/ament_cmake/cmake)
[0.050s] -- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter 
[0.128s] -- Found rclcpp: 29.5.0 (/opt/ros/kilted/share/rclcpp/cmake)
[0.156s] -- Found rosidl_generator_c: 4.9.4 (/opt/ros/kilted/share/rosidl_generator_c/cmake)
[0.166s] -- Found rosidl_generator_cpp: 4.9.4 (/opt/ros/kilted/share/rosidl_generator_cpp/cmake)
[0.177s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.191s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.257s] -- Found rmw_implementation_cmake: 7.8.2 (/opt/ros/kilted/share/rmw_implementation_cmake/cmake)
[0.258s] -- Found rmw_fastrtps_cpp: 9.3.2 (/opt/ros/kilted/share/rmw_fastrtps_cpp/cmake)
[0.297s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.368s] -- Found rosidl_default_generators: 1.7.1 (/opt/ros/kilted/share/rosidl_default_generators/cmake)
[0.376s] -- Found rosidl_adapter: 4.9.4 (/opt/ros/kilted/share/rosidl_adapter/cmake)
[0.640s] -- Found ament_cmake_ros_core: 0.14.3 (/opt/ros/kilted/share/ament_cmake_ros_core/cmake)
[0.787s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.847s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[1.079s] -- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter Development NumPy Development.Module Development.Embed 
[1.089s] [0mCMake Deprecation Warning at /opt/ros/kilted/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake:87 (message):
[1.089s]   ament_target_dependencies() is deprecated.  Use target_link_libraries()
[1.089s]   with modern CMake targets instead.  Try replacing this call with:
[1.089s] 
[1.090s]     target_link_libraries(arithmetic_server PUBLIC
[1.090s]       rclcpp::rclcpp
[1.090s]     )
[1.090s] 
[1.090s] Call Stack (most recent call first):
[1.090s]   CMakeLists.txt:29 (ament_target_dependencies)
[1.090s] 
[1.090s] [0m
[1.090s] [31mCMake Error at /opt/ros/kilted/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake:101 (message):
[1.090s]   ament_target_dependencies() the passed package name
[1.090s]   'my_calculator_interfaces' was not found before
[1.090s] Call Stack (most recent call first):
[1.090s]   CMakeLists.txt:29 (ament_target_dependencies)
[1.090s] 
[1.091s] [0m
[1.091s] -- Configuring incomplete, errors occurred!
[1.096s] Invoked command in '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator' returned '1': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/kilted:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/ROS2_WS/my_calculator -DCMAKE_INSTALL_PREFIX=/home/<USER>/ROS2_WS/my_calculator/install/my_calculator
