[  1%] Built target my_calculator__rosidl_generator_type_description
[  1%] Built target ament_cmake_python_copy_my_calculator
[  2%] [32m[1mLinking CXX executable arithmetic_server[0m
[  4%] [32mBuilding CXX object CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o[0m
[  5%] Built target my_calculator__cpp
[ 26%] Built target my_calculator__rosidl_generator_c
[ 47%] Built target my_calculator__rosidl_typesupport_fastrtps_cpp
[ 48%] Built target my_calculator__rosidl_typesupport_introspection_cpp
[ 57%] Built target my_calculator__rosidl_typesupport_introspection_c
[ 61%] Built target my_calculator__rosidl_typesupport_c
[ 70%] Built target my_calculator__rosidl_typesupport_cpp
[ 79%] Built target my_calculator__rosidl_typesupport_fastrtps_c
[ 79%] Built target my_calculator
[ 80%] Built target my_calculator__py
[ 88%] Built target my_calculator__rosidl_generator_py
running egg_info
writing my_calculator.egg-info/PKG-INFO
writing dependency_links to my_calculator.egg-info/dependency_links.txt
writing top-level names to my_calculator.egg-info/top_level.txt
reading manifest file 'my_calculator.egg-info/SOURCES.txt'
writing manifest file 'my_calculator.egg-info/SOURCES.txt'
[ 88%] Built target ament_cmake_python_build_my_calculator_egg
