[  1%] Built target my_calculator__rosidl_generator_type_description
[  4%] [32mBuilding CXX object CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o[0m
[  4%] [32mBuilding CXX object CMakeFiles/arithmetic_server.dir/src/arithmetic_server.cpp.o[0m
[  4%] Built target ament_cmake_python_copy_my_calculator
[ 25%] Built target my_calculator__rosidl_generator_c
[ 26%] Built target my_calculator__cpp
[ 35%] Built target my_calculator__rosidl_typesupport_c
[ 44%] Built target my_calculator__rosidl_typesupport_introspection_c
[ 52%] Built target my_calculator__rosidl_typesupport_fastrtps_c
[ 54%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/my_calculator/srv/multiply__type_support.cpp.o[0m
[ 55%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/my_calculator/srv/add__type_support.cpp.o[0m
[ 57%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/my_calculator/srv/divide__type_support.cpp.o[0m
[ 58%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/my_calculator/srv/detail/add__type_support.cpp.o[0m
[ 60%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/my_calculator/srv/detail/subtract__type_support.cpp.o[0m
[ 61%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/my_calculator/srv/subtract__type_support.cpp.o[0m
[ 63%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/my_calculator/srv/detail/dds_fastrtps/add__type_support.cpp.o[0m
[ 64%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/my_calculator/srv/detail/multiply__type_support.cpp.o[0m
[ 66%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/my_calculator/srv/detail/dds_fastrtps/subtract__type_support.cpp.o[0m
[ 67%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/my_calculator/srv/detail/divide__type_support.cpp.o[0m
running egg_info
writing my_calculator.egg-info/PKG-INFO
writing dependency_links to my_calculator.egg-info/dependency_links.txt
writing top-level names to my_calculator.egg-info/top_level.txt
[ 69%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/my_calculator/srv/detail/dds_fastrtps/multiply__type_support.cpp.o[0m
reading manifest file 'my_calculator.egg-info/SOURCES.txt'
writing manifest file 'my_calculator.egg-info/SOURCES.txt'
[ 69%] Built target ament_cmake_python_build_my_calculator_egg
[ 70%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/my_calculator/srv/detail/dds_fastrtps/divide__type_support.cpp.o[0m
[ 72%] [32m[1mLinking CXX shared library libmy_calculator__rosidl_typesupport_cpp.so[0m
[ 73%] Built target my_calculator__rosidl_typesupport_cpp
[ 75%] [32m[1mLinking CXX shared library libmy_calculator__rosidl_typesupport_introspection_cpp.so[0m
[ 76%] Built target my_calculator__rosidl_typesupport_introspection_cpp
[ 77%] [32m[1mLinking CXX shared library libmy_calculator__rosidl_typesupport_fastrtps_cpp.so[0m
[ 79%] Built target my_calculator__rosidl_typesupport_fastrtps_cpp
