[0.011s] Invoking command in '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/kilted:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ROS2_WS/my_calculator/build/my_calculator -- -j12 -l12
[0.064s] [  1%] Built target my_calculator__rosidl_generator_type_description
[0.064s] [  4%] [32mBuilding CXX object CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o[0m
[0.064s] [  4%] [32mBuilding CXX object CMakeFiles/arithmetic_server.dir/src/arithmetic_server.cpp.o[0m
[0.075s] [  4%] Built target ament_cmake_python_copy_my_calculator
[0.103s] [ 25%] Built target my_calculator__rosidl_generator_c
[0.132s] [ 26%] Built target my_calculator__cpp
[0.148s] [ 35%] Built target my_calculator__rosidl_typesupport_c
[0.151s] [ 44%] Built target my_calculator__rosidl_typesupport_introspection_c
[0.159s] [ 52%] Built target my_calculator__rosidl_typesupport_fastrtps_c
[0.193s] [ 54%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/my_calculator/srv/multiply__type_support.cpp.o[0m
[0.193s] [ 55%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/my_calculator/srv/add__type_support.cpp.o[0m
[0.195s] [ 57%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/my_calculator/srv/divide__type_support.cpp.o[0m
[0.195s] [ 58%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/my_calculator/srv/detail/add__type_support.cpp.o[0m
[0.196s] [ 60%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/my_calculator/srv/detail/subtract__type_support.cpp.o[0m
[0.196s] [ 61%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/my_calculator/srv/subtract__type_support.cpp.o[0m
[0.199s] [ 63%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/my_calculator/srv/detail/dds_fastrtps/add__type_support.cpp.o[0m
[0.202s] [ 64%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/my_calculator/srv/detail/multiply__type_support.cpp.o[0m
[0.207s] [ 66%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/my_calculator/srv/detail/dds_fastrtps/subtract__type_support.cpp.o[0m
[0.667s] In file included from [01m[K/home/<USER>/ROS2_WS/my_calculator/src/../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/add.hpp:9[m[K,
[0.667s]                  from [01m[K/home/<USER>/ROS2_WS/my_calculator/src/arithmetic_client.cpp:2[m[K:
[0.667s] [01m[K/home/<USER>/ROS2_WS/my_calculator/src/../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/./detail/add__traits.hpp:17:10:[m[K [01;31m[Kfatal error: [m[Kmy_calculator/srv/detail/add__struct.hpp: No such file or directory
[0.667s]    17 | #include [01;31m[K"my_calculator/srv/detail/add__struct.hpp"[m[K
[0.668s]       |          [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[0.668s] compilation terminated.
[0.676s] gmake[2]: *** [CMakeFiles/arithmetic_client.dir/build.make:76: CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o] Error 1
[0.676s] gmake[1]: *** [CMakeFiles/Makefile2:651: CMakeFiles/arithmetic_client.dir/all] Error 2
[0.676s] gmake[1]: *** Waiting for unfinished jobs....
[0.711s] [ 67%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/my_calculator/srv/detail/divide__type_support.cpp.o[0m
[0.800s] running egg_info
[0.849s] In file included from [01m[K/home/<USER>/ROS2_WS/my_calculator/src/../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/add.hpp:9[m[K,
[0.849s]                  from [01m[K/home/<USER>/ROS2_WS/my_calculator/src/arithmetic_server.cpp:2[m[K:
[0.849s] [01m[K/home/<USER>/ROS2_WS/my_calculator/src/../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/./detail/add__traits.hpp:17:10:[m[K [01;31m[Kfatal error: [m[Kmy_calculator/srv/detail/add__struct.hpp: No such file or directory
[0.850s]    17 | #include [01;31m[K"my_calculator/srv/detail/add__struct.hpp"[m[K
[0.850s]       |          [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[0.850s] compilation terminated.
[0.862s] writing my_calculator.egg-info/PKG-INFO
[0.862s] writing dependency_links to my_calculator.egg-info/dependency_links.txt
[0.863s] writing top-level names to my_calculator.egg-info/top_level.txt
[0.868s] gmake[2]: *** [CMakeFiles/arithmetic_server.dir/build.make:76: CMakeFiles/arithmetic_server.dir/src/arithmetic_server.cpp.o] Error 1
[0.868s] gmake[1]: *** [CMakeFiles/Makefile2:625: CMakeFiles/arithmetic_server.dir/all] Error 2
[0.897s] [ 69%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/my_calculator/srv/detail/dds_fastrtps/multiply__type_support.cpp.o[0m
[1.038s] reading manifest file 'my_calculator.egg-info/SOURCES.txt'
[1.040s] writing manifest file 'my_calculator.egg-info/SOURCES.txt'
[1.107s] [ 69%] Built target ament_cmake_python_build_my_calculator_egg
[1.118s] [ 70%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/my_calculator/srv/detail/dds_fastrtps/divide__type_support.cpp.o[0m
[1.308s] [ 72%] [32m[1mLinking CXX shared library libmy_calculator__rosidl_typesupport_cpp.so[0m
[1.420s] [ 73%] Built target my_calculator__rosidl_typesupport_cpp
[1.503s] [ 75%] [32m[1mLinking CXX shared library libmy_calculator__rosidl_typesupport_introspection_cpp.so[0m
[1.608s] [ 76%] Built target my_calculator__rosidl_typesupport_introspection_cpp
[1.886s] [ 77%] [32m[1mLinking CXX shared library libmy_calculator__rosidl_typesupport_fastrtps_cpp.so[0m
[1.972s] [ 79%] Built target my_calculator__rosidl_typesupport_fastrtps_cpp
[1.973s] gmake: *** [Makefile:146: all] Error 2
[1.975s] Invoked command in '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator' returned '2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/kilted:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ROS2_WS/my_calculator/build/my_calculator -- -j12 -l12
