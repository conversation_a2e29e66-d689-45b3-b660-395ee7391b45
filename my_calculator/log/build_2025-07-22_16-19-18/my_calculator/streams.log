[0.011s] Invoking command in '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/kilted:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ROS2_WS/my_calculator/build/my_calculator -- -j12 -l12
[0.040s] -- Found ament_cmake: 2.7.3 (/opt/ros/kilted/share/ament_cmake/cmake)
[0.066s] -- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter 
[0.146s] -- Found rclcpp: 29.5.0 (/opt/ros/kilted/share/rclcpp/cmake)
[0.173s] -- Found rosidl_generator_c: 4.9.4 (/opt/ros/kilted/share/rosidl_generator_c/cmake)
[0.182s] -- Found rosidl_generator_cpp: 4.9.4 (/opt/ros/kilted/share/rosidl_generator_cpp/cmake)
[0.193s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.207s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.274s] -- Found rmw_implementation_cmake: 7.8.2 (/opt/ros/kilted/share/rmw_implementation_cmake/cmake)
[0.276s] -- Found rmw_fastrtps_cpp: 9.3.2 (/opt/ros/kilted/share/rmw_fastrtps_cpp/cmake)
[0.315s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.386s] -- Found rosidl_default_generators: 1.7.1 (/opt/ros/kilted/share/rosidl_default_generators/cmake)
[0.394s] -- Found rosidl_adapter: 4.9.4 (/opt/ros/kilted/share/rosidl_adapter/cmake)
[0.653s] -- Found ament_cmake_ros_core: 0.14.3 (/opt/ros/kilted/share/ament_cmake_ros_core/cmake)
[0.802s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.861s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[1.088s] -- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter Development NumPy Development.Module Development.Embed 
[1.103s] -- Configuring done (1.1s)
[1.156s] -- Generating done (0.0s)
[1.160s] -- Build files have been written to: /home/<USER>/ROS2_WS/my_calculator/build/my_calculator
[1.195s] [  2%] [32mBuilding CXX object CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o[0m
[1.195s] [  2%] Built target my_calculator__rosidl_generator_type_description
[1.195s] [  4%] [32mBuilding CXX object CMakeFiles/arithmetic_server.dir/src/arithmetic_server.cpp.o[0m
[1.203s] [  4%] Built target ament_cmake_python_copy_my_calculator
[1.215s] [  5%] Built target my_calculator__cpp
[1.225s] [ 26%] Built target my_calculator__rosidl_generator_c
[1.241s] [ 27%] [34m[1mGenerating C type support for eProsima Fast-RTPS[0m
[1.242s] [ 29%] [34m[1mGenerating C++ type support dispatch for ROS interfaces[0m
[1.242s] [ 30%] [34m[1mGenerating C introspection for ROS interfaces[0m
[1.244s] [ 32%] [34m[1mGenerating C++ type support for eProsima Fast-RTPS[0m
[1.244s] [ 33%] [34m[1mGenerating C++ introspection for ROS interfaces[0m
[1.244s] [ 35%] [34m[1mGenerating C type support dispatch for ROS interfaces[0m
[1.539s] [01m[K/home/<USER>/ROS2_WS/my_calculator/src/arithmetic_client.cpp:2:10:[m[K [01;31m[Kfatal error: [m[Kmy_calculator/srv/add.hpp: No such file or directory
[1.539s]     2 | #include [01;31m[K<my_calculator/srv/add.hpp>[m[K
[1.539s]       |          [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[1.540s] compilation terminated.
[1.543s] gmake[2]: *** [CMakeFiles/arithmetic_client.dir/build.make:76: CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o] Error 1
[1.543s] gmake[1]: *** [CMakeFiles/Makefile2:651: CMakeFiles/arithmetic_client.dir/all] Error 2
[1.543s] gmake[1]: *** Waiting for unfinished jobs....
[1.558s] [01m[K/home/<USER>/ROS2_WS/my_calculator/src/arithmetic_server.cpp:2:10:[m[K [01;31m[Kfatal error: [m[Kmy_calculator/srv/add.hpp: No such file or directory
[1.558s]     2 | #include [01;31m[K<my_calculator/srv/add.hpp>[m[K
[1.558s]       |          [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[1.558s] compilation terminated.
[1.561s] gmake[2]: *** [CMakeFiles/arithmetic_server.dir/build.make:76: CMakeFiles/arithmetic_server.dir/src/arithmetic_server.cpp.o] Error 1
[1.561s] gmake[1]: *** [CMakeFiles/Makefile2:625: CMakeFiles/arithmetic_server.dir/all] Error 2
[1.639s] running egg_info
[1.677s] writing my_calculator.egg-info/PKG-INFO
[1.677s] writing dependency_links to my_calculator.egg-info/dependency_links.txt
[1.678s] writing top-level names to my_calculator.egg-info/top_level.txt
[1.680s] [ 36%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/my_calculator/srv/multiply__type_support.cpp.o[0m
[1.682s] [ 39%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/my_calculator/srv/add__type_support.cpp.o[0m
[1.682s] [ 39%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/my_calculator/srv/subtract__type_support.cpp.o[0m
[1.682s] [ 41%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/my_calculator/srv/divide__type_support.cpp.o[0m
[1.780s] reading manifest file 'my_calculator.egg-info/SOURCES.txt'
[1.781s] writing manifest file 'my_calculator.egg-info/SOURCES.txt'
[1.815s] [ 41%] Built target ament_cmake_python_build_my_calculator_egg
[1.883s] [ 44%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_c.dir/rosidl_typesupport_c/my_calculator/srv/subtract__type_support.cpp.o[0m
[1.884s] [ 44%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_c.dir/rosidl_typesupport_c/my_calculator/srv/add__type_support.cpp.o[0m
[1.884s] [ 45%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_c.dir/rosidl_typesupport_c/my_calculator/srv/multiply__type_support.cpp.o[0m
[1.885s] [ 47%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_c.dir/rosidl_typesupport_c/my_calculator/srv/divide__type_support.cpp.o[0m
[1.981s] [ 48%] [32m[1mLinking CXX shared library libmy_calculator__rosidl_typesupport_c.so[0m
[2.036s] [ 48%] Built target my_calculator__rosidl_typesupport_c
[2.091s] [ 50%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/my_calculator/srv/detail/add__type_support.cpp.o[0m
[2.092s] [ 51%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/my_calculator/srv/detail/multiply__type_support.cpp.o[0m
[2.094s] [ 52%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/my_calculator/srv/detail/subtract__type_support.cpp.o[0m
[2.095s] [ 54%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/my_calculator/srv/detail/divide__type_support.cpp.o[0m
[2.131s] [ 55%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/my_calculator/srv/detail/add__type_support.c.o[0m
[2.137s] [ 57%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/my_calculator/srv/detail/subtract__type_support.c.o[0m
[2.231s] [ 58%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/my_calculator/srv/detail/multiply__type_support.c.o[0m
[2.245s] [ 60%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/my_calculator/srv/detail/divide__type_support.c.o[0m
[2.337s] [ 61%] [32m[1mLinking C shared library libmy_calculator__rosidl_typesupport_introspection_c.so[0m
[2.400s] [ 61%] Built target my_calculator__rosidl_typesupport_introspection_c
[2.469s] [ 63%] [32m[1mLinking CXX shared library libmy_calculator__rosidl_typesupport_cpp.so[0m
[2.523s] [ 64%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/my_calculator/srv/detail/dds_fastrtps/add__type_support.cpp.o[0m
[2.526s] [ 66%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/my_calculator/srv/detail/dds_fastrtps/subtract__type_support.cpp.o[0m
[2.531s] [ 67%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/my_calculator/srv/detail/dds_fastrtps/divide__type_support.cpp.o[0m
[2.533s] [ 69%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/my_calculator/srv/detail/dds_fastrtps/multiply__type_support.cpp.o[0m
[2.547s] [ 70%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/my_calculator/srv/detail/add__type_support_c.cpp.o[0m
[2.561s] [ 72%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/my_calculator/srv/detail/subtract__type_support_c.cpp.o[0m
[2.569s] [ 73%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/my_calculator/srv/detail/multiply__type_support_c.cpp.o[0m
[2.666s] [ 73%] Built target my_calculator__rosidl_typesupport_cpp
[2.695s] [ 75%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/my_calculator/srv/detail/divide__type_support_c.cpp.o[0m
[3.039s] [ 76%] [32m[1mLinking CXX shared library libmy_calculator__rosidl_typesupport_introspection_cpp.so[0m
[3.202s] [ 76%] Built target my_calculator__rosidl_typesupport_introspection_cpp
[3.545s] [ 77%] [32m[1mLinking CXX shared library libmy_calculator__rosidl_typesupport_fastrtps_c.so[0m
[3.620s] [ 79%] [32m[1mLinking CXX shared library libmy_calculator__rosidl_typesupport_fastrtps_cpp.so[0m
[3.620s] [ 79%] Built target my_calculator__rosidl_typesupport_fastrtps_c
[3.697s] [ 79%] Built target my_calculator__rosidl_typesupport_fastrtps_cpp
[3.698s] gmake: *** [Makefile:146: all] Error 2
[3.700s] Invoked command in '/home/<USER>/ROS2_WS/my_calculator/build/my_calculator' returned '2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/kilted:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ROS2_WS/my_calculator/build/my_calculator -- -j12 -l12
