-- Found ament_cmake: 2.7.3 (/opt/ros/kilted/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter 
-- Found rclcpp: 29.5.0 (/opt/ros/kilted/share/rclcpp/cmake)
-- Found rosidl_generator_c: 4.9.4 (/opt/ros/kilted/share/rosidl_generator_c/cmake)
-- Found rosidl_generator_cpp: 4.9.4 (/opt/ros/kilted/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 7.8.2 (/opt/ros/kilted/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 9.3.2 (/opt/ros/kilted/share/rmw_fastrtps_cpp/cmake)
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found rosidl_default_generators: 1.7.1 (/opt/ros/kilted/share/rosidl_default_generators/cmake)
-- Found rosidl_adapter: 4.9.4 (/opt/ros/kilted/share/rosidl_adapter/cmake)
-- Found ament_cmake_ros_core: 0.14.3 (/opt/ros/kilted/share/ament_cmake_ros_core/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter Development NumPy Development.Module Development.Embed 
-- Configuring done (1.1s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/ROS2_WS/my_calculator/build/my_calculator
[  2%] [32mBuilding CXX object CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o[0m
[  2%] Built target my_calculator__rosidl_generator_type_description
[  4%] [32mBuilding CXX object CMakeFiles/arithmetic_server.dir/src/arithmetic_server.cpp.o[0m
[  4%] Built target ament_cmake_python_copy_my_calculator
[  5%] Built target my_calculator__cpp
[ 26%] Built target my_calculator__rosidl_generator_c
[ 27%] [34m[1mGenerating C type support for eProsima Fast-RTPS[0m
[ 29%] [34m[1mGenerating C++ type support dispatch for ROS interfaces[0m
[ 30%] [34m[1mGenerating C introspection for ROS interfaces[0m
[ 32%] [34m[1mGenerating C++ type support for eProsima Fast-RTPS[0m
[ 33%] [34m[1mGenerating C++ introspection for ROS interfaces[0m
[ 35%] [34m[1mGenerating C type support dispatch for ROS interfaces[0m
running egg_info
writing my_calculator.egg-info/PKG-INFO
writing dependency_links to my_calculator.egg-info/dependency_links.txt
writing top-level names to my_calculator.egg-info/top_level.txt
[ 36%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/my_calculator/srv/multiply__type_support.cpp.o[0m
[ 39%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/my_calculator/srv/add__type_support.cpp.o[0m
[ 39%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/my_calculator/srv/subtract__type_support.cpp.o[0m
[ 41%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/my_calculator/srv/divide__type_support.cpp.o[0m
reading manifest file 'my_calculator.egg-info/SOURCES.txt'
writing manifest file 'my_calculator.egg-info/SOURCES.txt'
[ 41%] Built target ament_cmake_python_build_my_calculator_egg
[ 44%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_c.dir/rosidl_typesupport_c/my_calculator/srv/subtract__type_support.cpp.o[0m
[ 44%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_c.dir/rosidl_typesupport_c/my_calculator/srv/add__type_support.cpp.o[0m
[ 45%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_c.dir/rosidl_typesupport_c/my_calculator/srv/multiply__type_support.cpp.o[0m
[ 47%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_c.dir/rosidl_typesupport_c/my_calculator/srv/divide__type_support.cpp.o[0m
[ 48%] [32m[1mLinking CXX shared library libmy_calculator__rosidl_typesupport_c.so[0m
[ 48%] Built target my_calculator__rosidl_typesupport_c
[ 50%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/my_calculator/srv/detail/add__type_support.cpp.o[0m
[ 51%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/my_calculator/srv/detail/multiply__type_support.cpp.o[0m
[ 52%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/my_calculator/srv/detail/subtract__type_support.cpp.o[0m
[ 54%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/my_calculator/srv/detail/divide__type_support.cpp.o[0m
[ 55%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/my_calculator/srv/detail/add__type_support.c.o[0m
[ 57%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/my_calculator/srv/detail/subtract__type_support.c.o[0m
[ 58%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/my_calculator/srv/detail/multiply__type_support.c.o[0m
[ 60%] [32mBuilding C object CMakeFiles/my_calculator__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/my_calculator/srv/detail/divide__type_support.c.o[0m
[ 61%] [32m[1mLinking C shared library libmy_calculator__rosidl_typesupport_introspection_c.so[0m
[ 61%] Built target my_calculator__rosidl_typesupport_introspection_c
[ 63%] [32m[1mLinking CXX shared library libmy_calculator__rosidl_typesupport_cpp.so[0m
[ 64%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/my_calculator/srv/detail/dds_fastrtps/add__type_support.cpp.o[0m
[ 66%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/my_calculator/srv/detail/dds_fastrtps/subtract__type_support.cpp.o[0m
[ 67%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/my_calculator/srv/detail/dds_fastrtps/divide__type_support.cpp.o[0m
[ 69%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/my_calculator/srv/detail/dds_fastrtps/multiply__type_support.cpp.o[0m
[ 70%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/my_calculator/srv/detail/add__type_support_c.cpp.o[0m
[ 72%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/my_calculator/srv/detail/subtract__type_support_c.cpp.o[0m
[ 73%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/my_calculator/srv/detail/multiply__type_support_c.cpp.o[0m
[ 73%] Built target my_calculator__rosidl_typesupport_cpp
[ 75%] [32mBuilding CXX object CMakeFiles/my_calculator__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/my_calculator/srv/detail/divide__type_support_c.cpp.o[0m
[ 76%] [32m[1mLinking CXX shared library libmy_calculator__rosidl_typesupport_introspection_cpp.so[0m
[ 76%] Built target my_calculator__rosidl_typesupport_introspection_cpp
[ 77%] [32m[1mLinking CXX shared library libmy_calculator__rosidl_typesupport_fastrtps_c.so[0m
[ 79%] [32m[1mLinking CXX shared library libmy_calculator__rosidl_typesupport_fastrtps_cpp.so[0m
[ 79%] Built target my_calculator__rosidl_typesupport_fastrtps_c
[ 79%] Built target my_calculator__rosidl_typesupport_fastrtps_cpp
