#include <rclcpp/rclcpp.hpp>
#include "../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/add.hpp"
#include "../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/subtract.hpp"
#include "../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/multiply.hpp"
#include "../build/my_calculator/rosidl_generator_cpp/my_calculator/srv/divide.hpp"

// Aliases for easier typing
using Add = my_calculator::srv::Add;
using Subtract = my_calculator::srv::Subtract;
using Multiply = my_calculator::srv::Multiply;
using Divide = my_calculator::srv::Divide;

class ArithmeticServer : public rclcpp::Node
{
public:
    ArithmeticServer() : Node("arithmetic_server")
    {
        RCLCPP_INFO(this->get_logger(), "Arithmetic Server node started.");

        // Create service handlers
        add_service_ = this->create_service<Add>(
            "add_numbers",
            std::bind(&ArithmeticServer::handleAdd, this, std::placeholders::_1, std::placeholders::_2));
        RCLCPP_INFO(this->get_logger(), "Service 'add_numbers' created.");

        subtract_service_ = this->create_service<Subtract>(
            "subtract_numbers",
            std::bind(&ArithmeticServer::handleSubtract, this, std::placeholders::_1, std::placeholders::_2));
        RCLCPP_INFO(this->get_logger(), "Service 'subtract_numbers' created.");

        multiply_service_ = this->create_service<Multiply>(
            "multiply_numbers",
            std::bind(&ArithmeticServer::handleMultiply, this, std::placeholders::_1, std::placeholders::_2));
        RCLCPP_INFO(this->get_logger(), "Service 'multiply_numbers' created.");

        divide_service_ = this->create_service<Divide>(
            "divide_numbers",
            std::bind(&ArithmeticServer::handleDivide, this, std::placeholders::_1, std::placeholders::_2));
        RCLCPP_INFO(this->get_logger(), "Service 'divide_numbers' created.");
    }

private:
    // Callback for Add service
    void handleAdd(const std::shared_ptr<Add::Request> request,
                   const std::shared_ptr<Add::Response> response)
    {
        response->result = request->a + request->b;
        response->success = true;
        response->message = "Addition successful.";
        RCLCPP_INFO(this->get_logger(), "Received add request: %d + %d = %d", request->a, request->b, response->result);
    }

    // Callback for Subtract service
    void handleSubtract(const std::shared_ptr<Subtract::Request> request,
                        const std::shared_ptr<Subtract::Response> response)
    {
        response->result = request->a - request->b;
        response->success = true;
        response->message = "Subtraction successful.";
        RCLCPP_INFO(this->get_logger(), "Received subtract request: %d - %d = %d", request->a, request->b, response->result);
    }

    // Callback for Multiply service
    void handleMultiply(const std::shared_ptr<Multiply::Request> request,
                        const std::shared_ptr<Multiply::Response> response)
    {
        response->result = request->a * request->b;
        response->success = true;
        response->message = "Multiplication successful.";
        RCLCPP_INFO(this->get_logger(), "Received multiply request: %d * %d = %d", request->a, request->b, response->result);
    }

    // Callback for Divide service
    void handleDivide(const std::shared_ptr<Divide::Request> request,
                      const std::shared_ptr<Divide::Response> response)
    {
        if (request->b == 0) {
            response->result = 0; // Or some other appropriate default/error value
            response->success = false;
            response->message = "Error: Division by zero is not allowed.";
            RCLCPP_WARN(this->get_logger(), "Received divide request: Division by zero detected (%d / %d).", request->a, request->b);
        } else {
            response->result = request->a / request->b;
            response->success = true;
            response->message = "Division successful.";
            RCLCPP_INFO(this->get_logger(), "Received divide request: %d / %d = %d", request->a, request->b, response->result);
        }
    }

    rclcpp::Service<Add>::SharedPtr add_service_;
    rclcpp::Service<Subtract>::SharedPtr subtract_service_;
    rclcpp::Service<Multiply>::SharedPtr multiply_service_;
    rclcpp::Service<Divide>::SharedPtr divide_service_;
};

int main(int argc, char **argv)
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<ArithmeticServer>();
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
}