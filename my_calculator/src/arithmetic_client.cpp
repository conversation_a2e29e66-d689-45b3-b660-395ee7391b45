#include <rclcpp/rclcpp.hpp>
#include <my_calculator/srv/add.hpp>
#include <my_calculator/srv/subtract.hpp>
#include <my_calculator/srv/multiply.hpp>
#include <my_calculator/srv/divide.hpp>
#include <iostream>
#include <string>
#include <limits> // For numeric_limits

// Aliases for easier typing
using Add = my_calculator::srv::Add;
using Subtract = my_calculator::srv::Subtract;
using Multiply = my_calculator::srv::Multiply;
using Divide = my_calculator::srv::Divide;

class ArithmeticClient : public rclcpp::Node
{
public:
    ArithmeticClient() : Node("arithmetic_client")
    {
        RCLCPP_INFO(this->get_logger(), "Arithmetic Client node started.");
        // Clients are created on demand in the interactive loop
    }

    void run_interactive_client()
    {
        std::string operation_str;
        int32_t num1, num2;

        while (rclcpp::ok()) {
            std::cout << "\nEnter operation (add, sub, mul, div) or 'quit' to exit: ";
            std::cin >> operation_str;

            if (operation_str == "quit") {
                RCLCPP_INFO(this->get_logger(), "Exiting client.");
                break;
            }

            std::cout << "Enter first number: ";
            while (!(std::cin >> num1)) {
                std::cout << "Invalid input. Please enter an integer: ";
                std::cin.clear();
                std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
            }

            std::cout << "Enter second number: ";
            while (!(std::cin >> num2)) {
                std::cout << "Invalid input. Please enter an integer: ";
                std::cin.clear();
                std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
            }

            bool service_found = false;

            if (operation_str == "add") {
                auto client = this->create_client<Add>("add_numbers");
                service_found = call_service(client, num1, num2, "Add");
            } else if (operation_str == "sub") {
                auto client = this->create_client<Subtract>("subtract_numbers");
                service_found = call_service(client, num1, num2, "Subtract");
            } else if (operation_str == "mul") {
                auto client = this->create_client<Multiply>("multiply_numbers");
                service_found = call_service(client, num1, num2, "Multiply");
            } else if (operation_str == "div") {
                auto client = this->create_client<Divide>("divide_numbers");
                service_found = call_service(client, num1, num2, "Divide");
            } else {
                RCLCPP_WARN(this->get_logger(), "Invalid operation: %s", operation_str.c_str());
            }

            if (!service_found) {
                 RCLCPP_ERROR(this->get_logger(), "Service not available. Ensure the arithmetic_server is running.");
            }

            // Clear the input buffer for the next iteration
            std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
        }
    }

private:
    template<typename ServiceT>
    bool call_service(typename rclcpp::Client<ServiceT>::SharedPtr client, int32_t a, int32_t b, const std::string& op_name)
    {
        while (!client->wait_for_service(std::chrono::seconds(1))) {
            if (!rclcpp::ok()) {
                RCLCPP_ERROR(this->get_logger(), "Client interrupted while waiting for service '%s'.", op_name.c_str());
                return false;
            }
            RCLCPP_INFO(this->get_logger(), "Waiting for service '%s' to be available...", op_name.c_str());
        }

        auto request = std::make_shared<typename ServiceT::Request>();
        request->a = a;
        request->b = b;

        RCLCPP_INFO(this->get_logger(), "Sending %s request: %d and %d", op_name.c_str(), a, b);

        auto result_future = client->async_send_request(request);
        // Wait for the result.
        if (rclcpp::spin_until_future_complete(this->get_node_base_interface(), result_future) ==
            rclcpp::FutureReturnCode::SUCCESS)
        {
            auto response = result_future.get();
            if (response->success) {
                RCLCPP_INFO(this->get_logger(), "Result for %s: %d (Message: %s)", op_name.c_str(), response->result, response->message.c_str());
            } else {
                RCLCPP_ERROR(this->get_logger(), "Operation %s failed: %s", op_name.c_str(), response->message.c_str());
            }
            return true;
        } else {
            RCLCPP_ERROR(this->get_logger(), "Failed to call service '%s'", op_name.c_str());
            return false;
        }
    }
};

int main(int argc, char **argv)
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<ArithmeticClient>();
    node->run_interactive_client();
    rclcpp::shutdown();
    return 0;
}