{"type_description_msg": {"type_description": {"type_name": "my_calculator/srv/Multiply_Request", "fields": [{"name": "a", "type": {"type_id": 6, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "b", "type": {"type_id": 6, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, "referenced_type_descriptions": []}, "type_hashes": [{"type_name": "my_calculator/srv/Multiply_Request", "hash_string": "RIHS01_057cf296f545c9b7f63ccad04ab419f847519c746332c80a16cd0065bcf36193"}]}