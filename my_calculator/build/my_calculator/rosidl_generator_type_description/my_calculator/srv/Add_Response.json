{"type_description_msg": {"type_description": {"type_name": "my_calculator/srv/Add_Response", "fields": [{"name": "result", "type": {"type_id": 6, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "success", "type": {"type_id": 15, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "message", "type": {"type_id": 17, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, "referenced_type_descriptions": []}, "type_hashes": [{"type_name": "my_calculator/srv/Add_Response", "hash_string": "RIHS01_27dc34e61f4595c36e8d8c49141438f0ae40fbe9784415cfcb3fac3963788b74"}]}