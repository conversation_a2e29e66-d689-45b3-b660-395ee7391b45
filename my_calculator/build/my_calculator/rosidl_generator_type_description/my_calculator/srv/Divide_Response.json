{"type_description_msg": {"type_description": {"type_name": "my_calculator/srv/Divide_Response", "fields": [{"name": "result", "type": {"type_id": 6, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "success", "type": {"type_id": 15, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "message", "type": {"type_id": 17, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, "referenced_type_descriptions": []}, "type_hashes": [{"type_name": "my_calculator/srv/Divide_Response", "hash_string": "RIHS01_2bcc0c1ab47b4980be65919a2f3f2c0fa1600e82566347eb54d33bae53641c9e"}]}