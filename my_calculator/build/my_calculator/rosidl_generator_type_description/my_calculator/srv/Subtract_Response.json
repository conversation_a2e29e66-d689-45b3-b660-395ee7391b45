{"type_description_msg": {"type_description": {"type_name": "my_calculator/srv/Subtract_Response", "fields": [{"name": "result", "type": {"type_id": 6, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "success", "type": {"type_id": 15, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "message", "type": {"type_id": 17, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, "referenced_type_descriptions": []}, "type_hashes": [{"type_name": "my_calculator/srv/Subtract_Response", "hash_string": "RIHS01_18f1a06f575a64502a95f8620ab966bbebe62122d6aa6cbfb00aadd89238dcb5"}]}