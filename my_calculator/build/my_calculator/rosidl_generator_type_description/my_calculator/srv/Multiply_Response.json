{"type_description_msg": {"type_description": {"type_name": "my_calculator/srv/Multiply_Response", "fields": [{"name": "result", "type": {"type_id": 6, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "success", "type": {"type_id": 15, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "message", "type": {"type_id": 17, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, "referenced_type_descriptions": []}, "type_hashes": [{"type_name": "my_calculator/srv/Multiply_Response", "hash_string": "RIHS01_5285af921bcde64b378d90994b947b960db291c3874ac959723d5260e9b30923"}]}