{"type_description_msg": {"type_description": {"type_name": "my_calculator/srv/Divide_Event", "fields": [{"name": "info", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "service_msgs/msg/ServiceEventInfo"}, "default_value": ""}, {"name": "request", "type": {"type_id": 97, "capacity": 1, "string_capacity": 0, "nested_type_name": "my_calculator/srv/Divide_Request"}, "default_value": ""}, {"name": "response", "type": {"type_id": 97, "capacity": 1, "string_capacity": 0, "nested_type_name": "my_calculator/srv/Divide_Response"}, "default_value": ""}]}, "referenced_type_descriptions": [{"type_name": "builtin_interfaces/msg/Time", "fields": [{"name": "sec", "type": {"type_id": 6, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "nanosec", "type": {"type_id": 7, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, {"type_name": "my_calculator/srv/Divide_Request", "fields": [{"name": "a", "type": {"type_id": 6, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "b", "type": {"type_id": 6, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, {"type_name": "my_calculator/srv/Divide_Response", "fields": [{"name": "result", "type": {"type_id": 6, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "success", "type": {"type_id": 15, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "message", "type": {"type_id": 17, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, {"type_name": "service_msgs/msg/ServiceEventInfo", "fields": [{"name": "event_type", "type": {"type_id": 3, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "stamp", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "builtin_interfaces/msg/Time"}, "default_value": ""}, {"name": "client_gid", "type": {"type_id": 51, "capacity": 16, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "sequence_number", "type": {"type_id": 8, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}]}, "type_hashes": [{"type_name": "my_calculator/srv/Divide_Event", "hash_string": "RIHS01_20f21d71c300612e3bd30949bbed5fefd8b65c906507955d6b84018c532b960d"}, {"type_name": "builtin_interfaces/msg/Time", "hash_string": "RIHS01_b106235e25a4c5ed35098aa0a61a3ee9c9b18d197f398b0e4206cea9acf9c197"}, {"type_name": "my_calculator/srv/Divide_Request", "hash_string": "RIHS01_a3d1a2171b594469d087424499bc5fc6c96138b6cbe76b621b0b8744fc19ee8f"}, {"type_name": "my_calculator/srv/Divide_Response", "hash_string": "RIHS01_2bcc0c1ab47b4980be65919a2f3f2c0fa1600e82566347eb54d33bae53641c9e"}, {"type_name": "service_msgs/msg/ServiceEventInfo", "hash_string": "RIHS01_41bcbbe07a75c9b52bc96bfd5c24d7f0fc0a08c0cb7921b3373c5732345a6f45"}]}