{"type_description_msg": {"type_description": {"type_name": "my_calculator/srv/Subtract_Request", "fields": [{"name": "a", "type": {"type_id": 6, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "b", "type": {"type_id": 6, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, "referenced_type_descriptions": []}, "type_hashes": [{"type_name": "my_calculator/srv/Subtract_Request", "hash_string": "RIHS01_ce9c65c5eb3823c7cba2bd845a6d43f311343e609f3483f7835982f134033f7a"}]}