{"type_description_msg": {"type_description": {"type_name": "my_calculator/srv/Divide_Request", "fields": [{"name": "a", "type": {"type_id": 6, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "b", "type": {"type_id": 6, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, "referenced_type_descriptions": []}, "type_hashes": [{"type_name": "my_calculator/srv/Divide_Request", "hash_string": "RIHS01_a3d1a2171b594469d087424499bc5fc6c96138b6cbe76b621b0b8744fc19ee8f"}]}