{"type_description_msg": {"type_description": {"type_name": "my_calculator/srv/Add", "fields": [{"name": "request_message", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "my_calculator/srv/Add_Request"}, "default_value": ""}, {"name": "response_message", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "my_calculator/srv/Add_Response"}, "default_value": ""}, {"name": "event_message", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "my_calculator/srv/Add_Event"}, "default_value": ""}]}, "referenced_type_descriptions": [{"type_name": "builtin_interfaces/msg/Time", "fields": [{"name": "sec", "type": {"type_id": 6, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "nanosec", "type": {"type_id": 7, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, {"type_name": "my_calculator/srv/Add_Event", "fields": [{"name": "info", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "service_msgs/msg/ServiceEventInfo"}, "default_value": ""}, {"name": "request", "type": {"type_id": 97, "capacity": 1, "string_capacity": 0, "nested_type_name": "my_calculator/srv/Add_Request"}, "default_value": ""}, {"name": "response", "type": {"type_id": 97, "capacity": 1, "string_capacity": 0, "nested_type_name": "my_calculator/srv/Add_Response"}, "default_value": ""}]}, {"type_name": "my_calculator/srv/Add_Request", "fields": [{"name": "a", "type": {"type_id": 6, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "b", "type": {"type_id": 6, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, {"type_name": "my_calculator/srv/Add_Response", "fields": [{"name": "result", "type": {"type_id": 6, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "success", "type": {"type_id": 15, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "message", "type": {"type_id": 17, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, {"type_name": "service_msgs/msg/ServiceEventInfo", "fields": [{"name": "event_type", "type": {"type_id": 3, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "stamp", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "builtin_interfaces/msg/Time"}, "default_value": ""}, {"name": "client_gid", "type": {"type_id": 51, "capacity": 16, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "sequence_number", "type": {"type_id": 8, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}]}, "type_hashes": [{"type_name": "my_calculator/srv/Add", "hash_string": "RIHS01_63a0ef98972e4405fe395da4a8391c53dbdf3549dd5173a6e0ebeacc134f19c8"}, {"type_name": "builtin_interfaces/msg/Time", "hash_string": "RIHS01_b106235e25a4c5ed35098aa0a61a3ee9c9b18d197f398b0e4206cea9acf9c197"}, {"type_name": "my_calculator/srv/Add_Event", "hash_string": "RIHS01_713b516f13d49a2e26208caa01bc73372bfd253977aa5e3fbb3c562a575371bf"}, {"type_name": "my_calculator/srv/Add_Request", "hash_string": "RIHS01_f195738f90be3cd890578633c5b484025b57943fdbdfe4df3d3a6149f2c8f53d"}, {"type_name": "my_calculator/srv/Add_Response", "hash_string": "RIHS01_27dc34e61f4595c36e8d8c49141438f0ae40fbe9784415cfcb3fac3963788b74"}, {"type_name": "service_msgs/msg/ServiceEventInfo", "hash_string": "RIHS01_41bcbbe07a75c9b52bc96bfd5c24d7f0fc0a08c0cb7921b3373c5732345a6f45"}]}