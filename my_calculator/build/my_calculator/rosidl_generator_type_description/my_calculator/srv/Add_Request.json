{"type_description_msg": {"type_description": {"type_name": "my_calculator/srv/Add_Request", "fields": [{"name": "a", "type": {"type_id": 6, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "b", "type": {"type_id": 6, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, "referenced_type_descriptions": []}, "type_hashes": [{"type_name": "my_calculator/srv/Add_Request", "hash_string": "RIHS01_f195738f90be3cd890578633c5b484025b57943fdbdfe4df3d3a6149f2c8f53d"}]}