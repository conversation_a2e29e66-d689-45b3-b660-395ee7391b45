{"type_description_msg": {"type_description": {"type_name": "my_calculator/srv/Multiply", "fields": [{"name": "request_message", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "my_calculator/srv/Multiply_Request"}, "default_value": ""}, {"name": "response_message", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "my_calculator/srv/Multiply_Response"}, "default_value": ""}, {"name": "event_message", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "my_calculator/srv/Multiply_Event"}, "default_value": ""}]}, "referenced_type_descriptions": [{"type_name": "builtin_interfaces/msg/Time", "fields": [{"name": "sec", "type": {"type_id": 6, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "nanosec", "type": {"type_id": 7, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, {"type_name": "my_calculator/srv/Multiply_Event", "fields": [{"name": "info", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "service_msgs/msg/ServiceEventInfo"}, "default_value": ""}, {"name": "request", "type": {"type_id": 97, "capacity": 1, "string_capacity": 0, "nested_type_name": "my_calculator/srv/Multiply_Request"}, "default_value": ""}, {"name": "response", "type": {"type_id": 97, "capacity": 1, "string_capacity": 0, "nested_type_name": "my_calculator/srv/Multiply_Response"}, "default_value": ""}]}, {"type_name": "my_calculator/srv/Multiply_Request", "fields": [{"name": "a", "type": {"type_id": 6, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "b", "type": {"type_id": 6, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, {"type_name": "my_calculator/srv/Multiply_Response", "fields": [{"name": "result", "type": {"type_id": 6, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "success", "type": {"type_id": 15, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "message", "type": {"type_id": 17, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}, {"type_name": "service_msgs/msg/ServiceEventInfo", "fields": [{"name": "event_type", "type": {"type_id": 3, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "stamp", "type": {"type_id": 1, "capacity": 0, "string_capacity": 0, "nested_type_name": "builtin_interfaces/msg/Time"}, "default_value": ""}, {"name": "client_gid", "type": {"type_id": 51, "capacity": 16, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}, {"name": "sequence_number", "type": {"type_id": 8, "capacity": 0, "string_capacity": 0, "nested_type_name": ""}, "default_value": ""}]}]}, "type_hashes": [{"type_name": "my_calculator/srv/Multiply", "hash_string": "RIHS01_1333e6dfa3c85144bb672fa23a531cbb99db75dfc2a8c3eb36fb215ecee6c270"}, {"type_name": "builtin_interfaces/msg/Time", "hash_string": "RIHS01_b106235e25a4c5ed35098aa0a61a3ee9c9b18d197f398b0e4206cea9acf9c197"}, {"type_name": "my_calculator/srv/Multiply_Event", "hash_string": "RIHS01_9637237b4c8c8b7c99fb98b8f116d4bf9cbeb237a35ff1c860eb6f6f22b46ed6"}, {"type_name": "my_calculator/srv/Multiply_Request", "hash_string": "RIHS01_057cf296f545c9b7f63ccad04ab419f847519c746332c80a16cd0065bcf36193"}, {"type_name": "my_calculator/srv/Multiply_Response", "hash_string": "RIHS01_5285af921bcde64b378d90994b947b960db291c3874ac959723d5260e9b30923"}, {"type_name": "service_msgs/msg/ServiceEventInfo", "hash_string": "RIHS01_41bcbbe07a75c9b52bc96bfd5c24d7f0fc0a08c0cb7921b3373c5732345a6f45"}]}