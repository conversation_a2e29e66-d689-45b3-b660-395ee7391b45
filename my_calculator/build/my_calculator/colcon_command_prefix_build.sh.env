AMENT_PREFIX_PATH=/opt/ros/kilted:/opt/ros/jazzy
CC_64=/home/<USER>/ti_sdk/linux-devkit/sysroots/x86_64-arago-linux/usr/bin/aarch64-oe-linux/aarch64-oe-linux-gcc --sysroot=/home/<USER>/ti_sdk/linux-devkit/sysroots/aarch64-oe-linux
CHROME_DESKTOP=code.desktop
CMAKE_PREFIX_PATH=/opt/ros/kilted/opt/gz_math_vendor:/opt/ros/kilted/opt/gz_utils_vendor:/opt/ros/kilted/opt/gz_cmake_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/Fast-DDS/install/fastdds:/home/<USER>/Fast-DDS/install/foonathan_memory_vendor:/home/<USER>/Fast-DDS/install/fastcdr
COLCON=1
COLCON_PREFIX_PATH=/home/<USER>/Fast-DDS/install
COLORTERM=truecolor
CROSS_COMPILE_32=/home/<USER>/ti_sdk/k3r5-devkit/sysroots/x86_64-arago-linux/usr/bin/arm-oe-eabi/arm-oe-eabi-
CROSS_COMPILE_64=/home/<USER>/ti_sdk/linux-devkit/sysroots/x86_64-arago-linux/usr/bin/aarch64-oe-linux/aarch64-oe-linux-
DBUS_SESSION_BUS_ADDRESS=unix:path=/run/user/1000/bus,guid=13c8833733f832d27a7c73e9687f00e8
DBUS_STARTER_ADDRESS=unix:path=/run/user/1000/bus,guid=13c8833733f832d27a7c73e9687f00e8
DBUS_STARTER_BUS_TYPE=session
DEBUGINFOD_URLS=https://debuginfod.ubuntu.com
DESKTOP_SESSION=ubuntu
DISPLAY=:1
GDK_BACKEND=x11
GDMSESSION=ubuntu
GIT_ASKPASS=/usr/share/code/resources/app/extensions/git/dist/askpass.sh
GNOME_DESKTOP_SESSION_ID=this-is-deprecated
GNOME_SHELL_SESSION_MODE=ubuntu
GPG_AGENT_INFO=/run/user/1000/gnupg/S.gpg-agent:0:1
GSM_SKIP_SSH_AGENT_WORKAROUND=true
GTK_MODULES=gail:atk-bridge
HOME=/home/<USER>
INVOCATION_ID=e212494725734083ac7a0695a3fbdefd
JOURNAL_STREAM=9:16178
LANG=en_US.UTF-8
LANGUAGE=en_AU:en
LC_ADDRESS=vi_VN.UTF-8
LC_ALL=en_US.UTF-8
LC_IDENTIFICATION=vi_VN.UTF-8
LC_MEASUREMENT=vi_VN.UTF-8
LC_MONETARY=vi_VN.UTF-8
LC_NAME=vi_VN.UTF-8
LC_NUMERIC=vi_VN.UTF-8
LC_PAPER=vi_VN.UTF-8
LC_TELEPHONE=vi_VN.UTF-8
LC_TIME=vi_VN.UTF-8
LD_LIBRARY_PATH=/opt/ros/kilted/opt/zenoh_cpp_vendor/lib:/opt/ros/kilted/opt/gz_math_vendor/lib:/opt/ros/kilted/opt/gz_utils_vendor/lib:/opt/ros/kilted/opt/rviz_ogre_vendor/lib:/opt/ros/kilted/lib/x86_64-linux-gnu:/opt/ros/kilted/opt/gz_cmake_vendor/lib:/opt/ros/kilted/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib:/home/<USER>/Fast-DDS/install/fastdds/lib:/home/<USER>/Fast-DDS/install/fastcdr/lib
LESSCLOSE=/usr/bin/lesspipe %s %s
LESSOPEN=| /usr/bin/lesspipe %s
LOGNAME=duythien
LS_COLORS=rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:
MANAGERPID=2429
MEMORY_PRESSURE_WATCH=/sys/fs/cgroup/user.slice/user-1000.slice/<EMAIL>/app.slice/app-gnome\x2dsession\x2dmanager.slice/<EMAIL>/memory.pressure
MEMORY_PRESSURE_WRITE=c29tZSAyMDAwMDAgMjAwMDAwMAA=
OLDPWD=/home/<USER>/ROS2_WS
ORIGINAL_XDG_CURRENT_DESKTOP=ubuntu:GNOME
PAPERSIZE=a4
PATH=/opt/ros/kilted/bin:/opt/ros/jazzy/bin:/home/<USER>/Fast-DDS/install/fastdds/bin:/home/<USER>/Fast-DDS/install/foonathan_memory_vendor/bin:/home/<USER>/.local/bin:/home/<USER>/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin
PKG_CONFIG_PATH=/home/<USER>/Fast-DDS/install/foonathan_memory_vendor/lib/x86_64-linux-gnu/pkgconfig:/home/<USER>/Fast-DDS/install/foonathan_memory_vendor/lib/pkgconfig
PWD=/home/<USER>/ROS2_WS/my_calculator/build/my_calculator
PYTHONPATH=/opt/ros/kilted/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages
QTWEBENGINE_DICTIONARIES_PATH=/usr/share/hunspell-bdic/
QT_ACCESSIBILITY=1
QT_IM_MODULE=ibus
ROS_APT_SOURCE_VERSION=1.1.0
ROS_AUTOMATIC_DISCOVERY_RANGE=SUBNET
ROS_DISTRO=kilted
ROS_PYTHON_VERSION=3
ROS_VERSION=2
SESSION_MANAGER=local/duythien-Ubuntu:@/tmp/.ICE-unix/2858,unix/duythien-Ubuntu:/tmp/.ICE-unix/2858
SHELL=/bin/bash
SHLVL=2
SSH_AUTH_SOCK=/run/user/1000/keyring/ssh
SYSROOT_64=/home/<USER>/ti_sdk/linux-devkit/sysroots/aarch64-oe-linux
SYSTEMD_EXEC_PID=2858
TERM=xterm-256color
TERM_PROGRAM=vscode
TERM_PROGRAM_VERSION=1.95.1
TILIX_ID=7576f55f-ecf4-429b-b4a2-30b035c92f0c
USER=duythien
USERNAME=duythien
VSCODE_GIT_ASKPASS_EXTRA_ARGS=
VSCODE_GIT_ASKPASS_MAIN=/usr/share/code/resources/app/extensions/git/dist/askpass-main.js
VSCODE_GIT_ASKPASS_NODE=/usr/share/code/code
VSCODE_GIT_IPC_HANDLE=/run/user/1000/vscode-git-fa7e2e94aa.sock
VTE_VERSION=7600
WINDOWPATH=2
XAUTHORITY=/run/user/1000/gdm/Xauthority
XDG_CONFIG_DIRS=/etc/xdg/xdg-ubuntu:/etc/xdg
XDG_CURRENT_DESKTOP=Unity
XDG_DATA_DIRS=/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop
XDG_MENU_PREFIX=gnome-
XDG_RUNTIME_DIR=/run/user/1000
XDG_SESSION_CLASS=user
XDG_SESSION_DESKTOP=ubuntu
XDG_SESSION_TYPE=x11
XMODIFIERS=@im=ibus
_=/home/<USER>/.local/bin/colcon
