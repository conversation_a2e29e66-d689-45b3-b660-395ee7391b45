file(REMOVE_RECURSE
  "CMakeFiles/my_calculator__rosidl_typesupport_c.dir/rosidl_typesupport_c/my_calculator/srv/add__type_support.cpp.o"
  "CMakeFiles/my_calculator__rosidl_typesupport_c.dir/rosidl_typesupport_c/my_calculator/srv/add__type_support.cpp.o.d"
  "CMakeFiles/my_calculator__rosidl_typesupport_c.dir/rosidl_typesupport_c/my_calculator/srv/divide__type_support.cpp.o"
  "CMakeFiles/my_calculator__rosidl_typesupport_c.dir/rosidl_typesupport_c/my_calculator/srv/divide__type_support.cpp.o.d"
  "CMakeFiles/my_calculator__rosidl_typesupport_c.dir/rosidl_typesupport_c/my_calculator/srv/multiply__type_support.cpp.o"
  "CMakeFiles/my_calculator__rosidl_typesupport_c.dir/rosidl_typesupport_c/my_calculator/srv/multiply__type_support.cpp.o.d"
  "CMakeFiles/my_calculator__rosidl_typesupport_c.dir/rosidl_typesupport_c/my_calculator/srv/subtract__type_support.cpp.o"
  "CMakeFiles/my_calculator__rosidl_typesupport_c.dir/rosidl_typesupport_c/my_calculator/srv/subtract__type_support.cpp.o.d"
  "libmy_calculator__rosidl_typesupport_c.pdb"
  "libmy_calculator__rosidl_typesupport_c.so"
  "rosidl_typesupport_c/my_calculator/srv/add__type_support.cpp"
  "rosidl_typesupport_c/my_calculator/srv/divide__type_support.cpp"
  "rosidl_typesupport_c/my_calculator/srv/multiply__type_support.cpp"
  "rosidl_typesupport_c/my_calculator/srv/subtract__type_support.cpp"
)

# Per-language clean rules from dependency scanning.
foreach(lang CXX)
  include(CMakeFiles/my_calculator__rosidl_typesupport_c.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
