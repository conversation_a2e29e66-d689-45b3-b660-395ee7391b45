file(REMOVE_RECURSE
  "CMakeFiles/my_calculator__cpp"
  "rosidl_generator_cpp/my_calculator/msg/rosidl_generator_cpp__visibility_control.hpp"
  "rosidl_generator_cpp/my_calculator/srv/add.hpp"
  "rosidl_generator_cpp/my_calculator/srv/detail/add__builder.hpp"
  "rosidl_generator_cpp/my_calculator/srv/detail/add__struct.hpp"
  "rosidl_generator_cpp/my_calculator/srv/detail/add__traits.hpp"
  "rosidl_generator_cpp/my_calculator/srv/detail/add__type_support.hpp"
  "rosidl_generator_cpp/my_calculator/srv/detail/divide__builder.hpp"
  "rosidl_generator_cpp/my_calculator/srv/detail/divide__struct.hpp"
  "rosidl_generator_cpp/my_calculator/srv/detail/divide__traits.hpp"
  "rosidl_generator_cpp/my_calculator/srv/detail/divide__type_support.hpp"
  "rosidl_generator_cpp/my_calculator/srv/detail/multiply__builder.hpp"
  "rosidl_generator_cpp/my_calculator/srv/detail/multiply__struct.hpp"
  "rosidl_generator_cpp/my_calculator/srv/detail/multiply__traits.hpp"
  "rosidl_generator_cpp/my_calculator/srv/detail/multiply__type_support.hpp"
  "rosidl_generator_cpp/my_calculator/srv/detail/subtract__builder.hpp"
  "rosidl_generator_cpp/my_calculator/srv/detail/subtract__struct.hpp"
  "rosidl_generator_cpp/my_calculator/srv/detail/subtract__traits.hpp"
  "rosidl_generator_cpp/my_calculator/srv/detail/subtract__type_support.hpp"
  "rosidl_generator_cpp/my_calculator/srv/divide.hpp"
  "rosidl_generator_cpp/my_calculator/srv/multiply.hpp"
  "rosidl_generator_cpp/my_calculator/srv/subtract.hpp"
)

# Per-language clean rules from dependency scanning.
foreach(lang )
  include(CMakeFiles/my_calculator__cpp.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
