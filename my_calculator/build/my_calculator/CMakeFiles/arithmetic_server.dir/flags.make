# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# compile CXX with /usr/bin/c++
CXX_DEFINES = -DDEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp -DFASTCDR_DYN_LINK -DROS_PACKAGE_NAME=\"my_calculator\"

CXX_INCLUDES = -isystem /opt/ros/kilted/include/rclcpp -isystem /opt/ros/kilted/include/builtin_interfaces -isystem /opt/ros/kilted/include/rosidl_runtime_c -isystem /opt/ros/kilted/include/rcutils -isystem /opt/ros/kilted/include/rosidl_typesupport_interface -isystem /home/<USER>/Fast-DDS/install/fastcdr/include -isystem /opt/ros/kilted/include/rosidl_runtime_cpp -isystem /opt/ros/kilted/include/rosidl_typesupport_fastrtps_cpp -isystem /opt/ros/kilted/include/rmw -isystem /opt/ros/kilted/include/rosidl_dynamic_typesupport -isystem /opt/ros/kilted/include/rosidl_typesupport_fastrtps_c -isystem /opt/ros/kilted/include/rosidl_typesupport_introspection_c -isystem /opt/ros/kilted/include/rosidl_typesupport_introspection_cpp -isystem /opt/ros/kilted/include/libstatistics_collector -isystem /opt/ros/kilted/include/rcl -isystem /opt/ros/kilted/include/rcl_interfaces -isystem /opt/ros/kilted/include/service_msgs -isystem /opt/ros/kilted/include/rcl_logging_interface -isystem /opt/ros/kilted/include/rcl_yaml_param_parser -isystem /opt/ros/kilted/include/type_description_interfaces -isystem /opt/ros/kilted/include/rcpputils -isystem /opt/ros/kilted/include/statistics_msgs -isystem /opt/ros/kilted/include/rosgraph_msgs -isystem /opt/ros/kilted/include/rosidl_typesupport_cpp -isystem /opt/ros/kilted/include/rosidl_typesupport_c -isystem /opt/ros/kilted/include/tracetools

CXX_FLAGS = -std=gnu++17

