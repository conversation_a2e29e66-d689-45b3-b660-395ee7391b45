# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/ROS2_WS/my_calculator

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/ROS2_WS/my_calculator/build/my_calculator

# Utility rule file for my_calculator__rosidl_generator_type_description.

# Include any custom commands dependencies for this target.
include CMakeFiles/my_calculator__rosidl_generator_type_description.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/my_calculator__rosidl_generator_type_description.dir/progress.make

CMakeFiles/my_calculator__rosidl_generator_type_description: rosidl_generator_type_description/my_calculator/srv/Add.json
CMakeFiles/my_calculator__rosidl_generator_type_description: rosidl_generator_type_description/my_calculator/srv/Subtract.json
CMakeFiles/my_calculator__rosidl_generator_type_description: rosidl_generator_type_description/my_calculator/srv/Multiply.json
CMakeFiles/my_calculator__rosidl_generator_type_description: rosidl_generator_type_description/my_calculator/srv/Divide.json

rosidl_generator_type_description/my_calculator/srv/Add.json: /opt/ros/kilted/lib/rosidl_generator_type_description/rosidl_generator_type_description
rosidl_generator_type_description/my_calculator/srv/Add.json: /opt/ros/kilted/lib/python3.12/site-packages/rosidl_generator_type_description/__init__.py
rosidl_generator_type_description/my_calculator/srv/Add.json: rosidl_adapter/my_calculator/srv/Add.idl
rosidl_generator_type_description/my_calculator/srv/Add.json: rosidl_adapter/my_calculator/srv/Subtract.idl
rosidl_generator_type_description/my_calculator/srv/Add.json: rosidl_adapter/my_calculator/srv/Multiply.idl
rosidl_generator_type_description/my_calculator/srv/Add.json: rosidl_adapter/my_calculator/srv/Divide.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/ROS2_WS/my_calculator/build/my_calculator/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating type hashes for ROS interfaces"
	/usr/bin/python3 /opt/ros/kilted/lib/rosidl_generator_type_description/rosidl_generator_type_description --generator-arguments-file /home/<USER>/ROS2_WS/my_calculator/build/my_calculator/rosidl_generator_type_description__arguments.json

rosidl_generator_type_description/my_calculator/srv/Subtract.json: rosidl_generator_type_description/my_calculator/srv/Add.json
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_type_description/my_calculator/srv/Subtract.json

rosidl_generator_type_description/my_calculator/srv/Multiply.json: rosidl_generator_type_description/my_calculator/srv/Add.json
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_type_description/my_calculator/srv/Multiply.json

rosidl_generator_type_description/my_calculator/srv/Divide.json: rosidl_generator_type_description/my_calculator/srv/Add.json
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_type_description/my_calculator/srv/Divide.json

my_calculator__rosidl_generator_type_description: CMakeFiles/my_calculator__rosidl_generator_type_description
my_calculator__rosidl_generator_type_description: rosidl_generator_type_description/my_calculator/srv/Add.json
my_calculator__rosidl_generator_type_description: rosidl_generator_type_description/my_calculator/srv/Divide.json
my_calculator__rosidl_generator_type_description: rosidl_generator_type_description/my_calculator/srv/Multiply.json
my_calculator__rosidl_generator_type_description: rosidl_generator_type_description/my_calculator/srv/Subtract.json
my_calculator__rosidl_generator_type_description: CMakeFiles/my_calculator__rosidl_generator_type_description.dir/build.make
.PHONY : my_calculator__rosidl_generator_type_description

# Rule to build all files generated by this target.
CMakeFiles/my_calculator__rosidl_generator_type_description.dir/build: my_calculator__rosidl_generator_type_description
.PHONY : CMakeFiles/my_calculator__rosidl_generator_type_description.dir/build

CMakeFiles/my_calculator__rosidl_generator_type_description.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/my_calculator__rosidl_generator_type_description.dir/cmake_clean.cmake
.PHONY : CMakeFiles/my_calculator__rosidl_generator_type_description.dir/clean

CMakeFiles/my_calculator__rosidl_generator_type_description.dir/depend:
	cd /home/<USER>/ROS2_WS/my_calculator/build/my_calculator && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/ROS2_WS/my_calculator /home/<USER>/ROS2_WS/my_calculator /home/<USER>/ROS2_WS/my_calculator/build/my_calculator /home/<USER>/ROS2_WS/my_calculator/build/my_calculator /home/<USER>/ROS2_WS/my_calculator/build/my_calculator/CMakeFiles/my_calculator__rosidl_generator_type_description.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/my_calculator__rosidl_generator_type_description.dir/depend

