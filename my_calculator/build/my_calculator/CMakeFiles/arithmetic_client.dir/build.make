# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/ROS2_WS/my_calculator

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/ROS2_WS/my_calculator/build/my_calculator

# Include any dependencies generated for this target.
include CMakeFiles/arithmetic_client.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/arithmetic_client.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/arithmetic_client.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/arithmetic_client.dir/flags.make

CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o: CMakeFiles/arithmetic_client.dir/flags.make
CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o: /home/<USER>/ROS2_WS/my_calculator/src/arithmetic_client.cpp
CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o: CMakeFiles/arithmetic_client.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/ROS2_WS/my_calculator/build/my_calculator/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o -MF CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o.d -o CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o -c /home/<USER>/ROS2_WS/my_calculator/src/arithmetic_client.cpp

CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/ROS2_WS/my_calculator/src/arithmetic_client.cpp > CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.i

CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/ROS2_WS/my_calculator/src/arithmetic_client.cpp -o CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.s

# Object files for target arithmetic_client
arithmetic_client_OBJECTS = \
"CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o"

# External object files for target arithmetic_client
arithmetic_client_EXTERNAL_OBJECTS =

arithmetic_client: CMakeFiles/arithmetic_client.dir/src/arithmetic_client.cpp.o
arithmetic_client: CMakeFiles/arithmetic_client.dir/build.make
arithmetic_client: /opt/ros/kilted/lib/librclcpp.so
arithmetic_client: /opt/ros/kilted/lib/liblibstatistics_collector.so
arithmetic_client: /opt/ros/kilted/lib/librcl.so
arithmetic_client: /opt/ros/kilted/lib/librmw_implementation.so
arithmetic_client: /opt/ros/kilted/lib/libtype_description_interfaces__rosidl_typesupport_fastrtps_c.so
arithmetic_client: /opt/ros/kilted/lib/libtype_description_interfaces__rosidl_typesupport_introspection_c.so
arithmetic_client: /opt/ros/kilted/lib/libtype_description_interfaces__rosidl_typesupport_fastrtps_cpp.so
arithmetic_client: /opt/ros/kilted/lib/libtype_description_interfaces__rosidl_typesupport_introspection_cpp.so
arithmetic_client: /opt/ros/kilted/lib/libtype_description_interfaces__rosidl_typesupport_cpp.so
arithmetic_client: /opt/ros/kilted/lib/libtype_description_interfaces__rosidl_generator_py.so
arithmetic_client: /opt/ros/kilted/lib/libtype_description_interfaces__rosidl_typesupport_c.so
arithmetic_client: /opt/ros/kilted/lib/libtype_description_interfaces__rosidl_generator_c.so
arithmetic_client: /opt/ros/kilted/lib/librcl_interfaces__rosidl_typesupport_fastrtps_c.so
arithmetic_client: /opt/ros/kilted/lib/librcl_interfaces__rosidl_typesupport_introspection_c.so
arithmetic_client: /opt/ros/kilted/lib/librcl_interfaces__rosidl_typesupport_fastrtps_cpp.so
arithmetic_client: /opt/ros/kilted/lib/librcl_interfaces__rosidl_typesupport_introspection_cpp.so
arithmetic_client: /opt/ros/kilted/lib/librcl_interfaces__rosidl_typesupport_cpp.so
arithmetic_client: /opt/ros/kilted/lib/librcl_interfaces__rosidl_generator_py.so
arithmetic_client: /opt/ros/kilted/lib/librcl_interfaces__rosidl_typesupport_c.so
arithmetic_client: /opt/ros/kilted/lib/librcl_interfaces__rosidl_generator_c.so
arithmetic_client: /opt/ros/kilted/lib/libservice_msgs__rosidl_typesupport_fastrtps_c.so
arithmetic_client: /opt/ros/kilted/lib/libservice_msgs__rosidl_typesupport_introspection_c.so
arithmetic_client: /opt/ros/kilted/lib/libservice_msgs__rosidl_typesupport_fastrtps_cpp.so
arithmetic_client: /opt/ros/kilted/lib/libservice_msgs__rosidl_typesupport_introspection_cpp.so
arithmetic_client: /opt/ros/kilted/lib/libservice_msgs__rosidl_typesupport_cpp.so
arithmetic_client: /opt/ros/kilted/lib/libservice_msgs__rosidl_typesupport_c.so
arithmetic_client: /opt/ros/kilted/lib/libservice_msgs__rosidl_generator_c.so
arithmetic_client: /opt/ros/kilted/lib/librcl_yaml_param_parser.so
arithmetic_client: /opt/ros/kilted/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_c.so
arithmetic_client: /opt/ros/kilted/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_cpp.so
arithmetic_client: /opt/ros/kilted/lib/librosgraph_msgs__rosidl_typesupport_introspection_c.so
arithmetic_client: /opt/ros/kilted/lib/librosgraph_msgs__rosidl_typesupport_introspection_cpp.so
arithmetic_client: /opt/ros/kilted/lib/librosgraph_msgs__rosidl_typesupport_cpp.so
arithmetic_client: /opt/ros/kilted/lib/librosgraph_msgs__rosidl_generator_py.so
arithmetic_client: /opt/ros/kilted/lib/librosgraph_msgs__rosidl_typesupport_c.so
arithmetic_client: /opt/ros/kilted/lib/librosgraph_msgs__rosidl_generator_c.so
arithmetic_client: /opt/ros/kilted/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_c.so
arithmetic_client: /opt/ros/kilted/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_cpp.so
arithmetic_client: /opt/ros/kilted/lib/libstatistics_msgs__rosidl_typesupport_introspection_c.so
arithmetic_client: /opt/ros/kilted/lib/libstatistics_msgs__rosidl_typesupport_introspection_cpp.so
arithmetic_client: /opt/ros/kilted/lib/libstatistics_msgs__rosidl_typesupport_cpp.so
arithmetic_client: /opt/ros/kilted/lib/libstatistics_msgs__rosidl_generator_py.so
arithmetic_client: /opt/ros/kilted/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so
arithmetic_client: /opt/ros/kilted/lib/librosidl_typesupport_fastrtps_c.so
arithmetic_client: /opt/ros/kilted/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so
arithmetic_client: /opt/ros/kilted/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so
arithmetic_client: /opt/ros/kilted/lib/librosidl_typesupport_fastrtps_cpp.so
arithmetic_client: /opt/ros/kilted/lib/librmw.so
arithmetic_client: /opt/ros/kilted/lib/librosidl_dynamic_typesupport.so
arithmetic_client: /home/<USER>/Fast-DDS/install/fastcdr/lib/libfastcdr.so.2.2.5
arithmetic_client: /opt/ros/kilted/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so
arithmetic_client: /opt/ros/kilted/lib/librosidl_typesupport_introspection_cpp.so
arithmetic_client: /opt/ros/kilted/lib/librosidl_typesupport_introspection_c.so
arithmetic_client: /opt/ros/kilted/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so
arithmetic_client: /opt/ros/kilted/lib/librosidl_typesupport_cpp.so
arithmetic_client: /opt/ros/kilted/lib/libbuiltin_interfaces__rosidl_generator_py.so
arithmetic_client: /opt/ros/kilted/lib/libstatistics_msgs__rosidl_typesupport_c.so
arithmetic_client: /opt/ros/kilted/lib/libbuiltin_interfaces__rosidl_typesupport_c.so
arithmetic_client: /opt/ros/kilted/lib/libstatistics_msgs__rosidl_generator_c.so
arithmetic_client: /opt/ros/kilted/lib/libbuiltin_interfaces__rosidl_generator_c.so
arithmetic_client: /opt/ros/kilted/lib/librosidl_typesupport_c.so
arithmetic_client: /opt/ros/kilted/lib/librcpputils.so
arithmetic_client: /opt/ros/kilted/lib/librosidl_runtime_c.so
arithmetic_client: /opt/ros/kilted/lib/libtracetools.so
arithmetic_client: /opt/ros/kilted/lib/librcl_logging_interface.so
arithmetic_client: /opt/ros/kilted/lib/librcutils.so
arithmetic_client: CMakeFiles/arithmetic_client.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/ROS2_WS/my_calculator/build/my_calculator/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable arithmetic_client"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/arithmetic_client.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/arithmetic_client.dir/build: arithmetic_client
.PHONY : CMakeFiles/arithmetic_client.dir/build

CMakeFiles/arithmetic_client.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/arithmetic_client.dir/cmake_clean.cmake
.PHONY : CMakeFiles/arithmetic_client.dir/clean

CMakeFiles/arithmetic_client.dir/depend:
	cd /home/<USER>/ROS2_WS/my_calculator/build/my_calculator && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/ROS2_WS/my_calculator /home/<USER>/ROS2_WS/my_calculator /home/<USER>/ROS2_WS/my_calculator/build/my_calculator /home/<USER>/ROS2_WS/my_calculator/build/my_calculator /home/<USER>/ROS2_WS/my_calculator/build/my_calculator/CMakeFiles/arithmetic_client.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/arithmetic_client.dir/depend

