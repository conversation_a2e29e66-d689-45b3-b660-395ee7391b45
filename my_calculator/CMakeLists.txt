cmake_minimum_required(VERSION 3.8 FATAL_ERROR)
project(my_calculator)

if(CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD ${CMAKE_CXX_STANDARD})
else()
  set(CMAKE_CXX_STANDARD 17) # Use C++17 standard
endif()

# Find ROS 2 packages
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(rosidl_default_generators REQUIRED) # For generating C++ headers from .srv files

# Specify include directories for generated service messages
# ament_auto_find_build_dependencies() <-- REMOVE THIS LINE

# Declare service files
rosidl_generate_interfaces(my_calculator
  "srv/Add.srv"
  "srv/Subtract.srv"
  "srv/Multiply.srv"
  "srv/Divide.srv"
  DEPENDENCIES builtin_interfaces # Standard ROS 2 message types, often needed
)

# Add the server executable
add_executable(arithmetic_server src/arithmetic_server.cpp)
ament_target_dependencies(arithmetic_server rclcpp my_calculator_interfaces) # my_calculator_interfaces will be generated

# Add the client executable
add_executable(arithmetic_client src/arithmetic_client.cpp)
ament_target_dependencies(arithmetic_client rclcpp my_calculator_interfaces)

# Install executables
install(TARGETS
  arithmetic_server
  arithmetic_client
  DESTINATION lib/${PROJECT_NAME}
)

# Install service files
install(
  DIRECTORY srv/
  DESTINATION share/${PROJECT_NAME}
)

# Mark directory for ament_cmake
ament_export_dependencies(rclcpp my_calculator_interfaces)

ament_package() 